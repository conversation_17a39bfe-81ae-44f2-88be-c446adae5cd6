# 📋 Documentação Front-End - Endpoint de Estoque de Equipamentos para IA

## 🎯 Visão Geral

Este documento contém **todas as informações necessárias** para que o front-end se comunique com o endpoint de envio de estoque de equipamentos para a IA.

## 🔗 Informações do Endpoint

### **URL Completa**
```
POST /psec/aparelhos/enviar-estoque-ia
```

### **Base URL**
- **Desenvolvimento:** `http://localhost:8080/psec/aparelhos/enviar-estoque-ia`
- **Produção:** `https://[SEU_DOMINIO]/psec/aparelhos/enviar-estoque-ia`

## 📤 Requisição HTTP

### **Método**
```
POST
```

### **Headers Obrigatórios**
```http
Content-Type: application/json
empresaId: [ID_DA_EMPRESA]
```

### **Headers de Autenticação**
```http
Authorization: Bearer [TOKEN_JWT]
```
*Ou conforme o padrão de autenticação do projeto*

## 📋 Estrutura dos Dados

### **Payload Principal (JSON)**
```json
{
  "equipments": [
    {
      // Campos obrigatórios
      "id": "002",
      "quantity": 15,
      "isActive": true,
      
      // Campos opcionais
      "brand": "Technogym",
      "editedName": "Anilha Premium Technogym",
      "hasSafetySystem": false,
      "level": "beginner",
      "targetMuscles": ["bíceps", "tríceps", "peitorais"],
      "weightType": "anilhas",
      "mechanism": "alavanca",
      "trajectory": "linear",
      "allowsUnilateral": true,
      "adjustable": false,
      "multiFunction": false,
      "requiresInstructor": false
    }
  ],
  "changed_by": "Nome do Usuário"
}
```

## 🔧 Campos Detalhados

### **Campos Obrigatórios (por equipamento)**

| Campo | Tipo | Descrição | Exemplo |
|-------|------|-----------|---------|
| `id` | `string` | ID único do equipamento | `"002"` |
| `quantity` | `integer` | Quantidade disponível (≥ 0) | `15` |
| `isActive` | `boolean` | Status ativo/inativo | `true` |

### **Campos Opcionais (por equipamento)**

| Campo | Tipo | Valores Permitidos | Exemplo |
|-------|------|-------------------|---------|
| `hasSafetySystem` | `boolean` | `true`, `false` | `true` |
| `editedName` | `string` | Texto livre | `"Supino Inclinado Premium"` |
| `weightType` | `string` | `"anilhas"`, `"peso embutido"`, `"carga livre"`, `"elástico"` | `"anilhas"` |
| `mechanism` | `string` | `"alavanca"`, `"articulado"`, `"cabos e polias"` | `"alavanca"` |
| `trajectory` | `string` | `"convergente"`, `"divergente"`, `"linear"`, `"arco/circular"`, `"livre"` | `"linear"` |
| `level` | `string` | `"beginner"`, `"intermediate"`, `"advanced"` | `"intermediate"` |
| `allowsUnilateral` | `boolean` | `true`, `false` | `true` |
| `adjustable` | `boolean` | `true`, `false` | `true` |
| `multiFunction` | `boolean` | `true`, `false` | `false` |
| `requiresInstructor` | `boolean` | `true`, `false` | `false` |
| `targetMuscles` | `array[string]` | Lista de músculos | `["peitoral", "tríceps"]` |
| `brand` | `string` | Texto livre | `"Technogym"` |

### **Campo Global Opcional**

| Campo | Tipo | Descrição | Exemplo |
|-------|------|-----------|---------|
| `changed_by` | `string` | Nome do usuário que fez a alteração | `"João Silva"` |

## 📨 Respostas do Servidor

### **Sucesso (200)**
```json
{
  "success": true,
  "data": "Estoque de equipamentos enviado com sucesso para IA!",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### **Erro de Validação (400)**
```json
{
  "success": false,
  "message": "O ID do equipamento é obrigatório",
  "chaveExcecao": "VALIDACAO_ERRO",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### **Erro de Permissão (403)**
```json
{
  "success": false,
  "message": "Usuário não possui permissão para acessar este recurso",
  "chaveExcecao": "PERMISSAO_NEGADA",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### **Erro Interno (500)**
```json
{
  "success": false,
  "message": "Erro ao enviar estoque de equipamentos para IA: [detalhes]",
  "chaveExcecao": "ERRO_INTERNO",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## ⚠️ Validações Importantes

### **Validações do Back-End**
1. ✅ Configuração IA deve estar habilitada
2. ✅ Usuário deve ter permissão `APARELHOS`
3. ✅ `empresaId` é obrigatório no header
4. ✅ Lista de equipamentos não pode estar vazia
5. ✅ Cada equipamento deve ter `id`, `quantity` e `isActive`
6. ✅ `quantity` deve ser ≥ 0
7. ✅ Campos enum devem ter valores válidos

### **Validações Recomendadas no Front-End**
```javascript
// Exemplo de validação JavaScript
function validarEquipamento(equipment) {
  const erros = [];
  
  // Campos obrigatórios
  if (!equipment.id || equipment.id.trim() === '') {
    erros.push('ID do equipamento é obrigatório');
  }
  
  if (equipment.quantity === null || equipment.quantity === undefined || equipment.quantity < 0) {
    erros.push('Quantidade deve ser maior ou igual a 0');
  }
  
  if (equipment.isActive === null || equipment.isActive === undefined) {
    erros.push('Status ativo é obrigatório');
  }
  
  // Validações de enum
  const weightTypes = ['anilhas', 'peso embutido', 'carga livre', 'elástico'];
  if (equipment.weightType && !weightTypes.includes(equipment.weightType)) {
    erros.push('Tipo de peso inválido');
  }
  
  const levels = ['beginner', 'intermediate', 'advanced'];
  if (equipment.level && !levels.includes(equipment.level)) {
    erros.push('Nível de complexidade inválido');
  }
  
  return erros;
}
```

## 💻 Exemplos de Implementação

### **JavaScript/Fetch**
```javascript
async function enviarEstoqueEquipamentos(equipamentos, empresaId, changedBy) {
  try {
    const payload = {
      equipments: equipamentos,
      changed_by: changedBy
    };

    const response = await fetch('/psec/aparelhos/enviar-estoque-ia', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'empresaId': empresaId,
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (response.ok) {
      console.log('Sucesso:', result.data);
      return { success: true, data: result.data };
    } else {
      console.error('Erro:', result.message);
      return { success: false, error: result.message };
    }
  } catch (error) {
    console.error('Erro de rede:', error);
    return { success: false, error: 'Erro de conexão' };
  }
}

// Exemplo de uso
const equipamentos = [
  {
    id: "002",
    quantity: 10,
    isActive: true,
    brand: "Technogym",
    level: "beginner"
  },
  {
    id: "050",
    quantity: 5,
    isActive: true,
    brand: "Life Fitness",
    level: "intermediate"
  }
];

enviarEstoqueEquipamentos(equipamentos, 123, "João Silva");
```

### **jQuery/AJAX**
```javascript
function enviarEstoqueEquipamentosJQuery(equipamentos, empresaId, changedBy) {
  const payload = {
    equipments: equipamentos,
    changed_by: changedBy
  };

  $.ajax({
    url: '/psec/aparelhos/enviar-estoque-ia',
    type: 'POST',
    contentType: 'application/json',
    headers: {
      'empresaId': empresaId,
      'Authorization': 'Bearer ' + getAuthToken()
    },
    data: JSON.stringify(payload),
    success: function(response) {
      console.log('Sucesso:', response.data);
      // Tratar sucesso
    },
    error: function(xhr, status, error) {
      const response = xhr.responseJSON;
      console.error('Erro:', response ? response.message : error);
      // Tratar erro
    }
  });
}
```

### **React/Axios**
```javascript
import axios from 'axios';

const enviarEstoqueEquipamentos = async (equipamentos, empresaId, changedBy) => {
  try {
    const payload = {
      equipments: equipamentos,
      changed_by: changedBy
    };

    const response = await axios.post('/psec/aparelhos/enviar-estoque-ia', payload, {
      headers: {
        'Content-Type': 'application/json',
        'empresaId': empresaId,
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });

    return { success: true, data: response.data.data };
  } catch (error) {
    const message = error.response?.data?.message || 'Erro de conexão';
    return { success: false, error: message };
  }
};

// Hook React
const useEstoqueEquipamentos = () => {
  const [loading, setLoading] = useState(false);

  const enviar = async (equipamentos, empresaId, changedBy) => {
    setLoading(true);
    try {
      const result = await enviarEstoqueEquipamentos(equipamentos, empresaId, changedBy);
      return result;
    } finally {
      setLoading(false);
    }
  };

  return { enviar, loading };
};
```

## 🔍 Casos de Teste

### **Caso 1: Envio Básico (Mínimo)**
```json
{
  "equipments": [
    {
      "id": "001",
      "quantity": 5,
      "isActive": true
    }
  ]
}
```

### **Caso 2: Envio Completo (Todos os Campos)**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 15,
      "isActive": true,
      "hasSafetySystem": true,
      "editedName": "Anilha Premium Technogym",
      "weightType": "anilhas",
      "mechanism": "alavanca",
      "trajectory": "linear",
      "level": "beginner",
      "allowsUnilateral": false,
      "adjustable": true,
      "multiFunction": false,
      "requiresInstructor": false,
      "targetMuscles": ["bíceps", "tríceps", "peitorais"],
      "brand": "Technogym"
    }
  ],
  "changed_by": "Administrador Sistema"
}
```

### **Caso 3: Múltiplos Equipamentos**
```json
{
  "equipments": [
    {
      "id": "001",
      "quantity": 10,
      "isActive": true,
      "brand": "Technogym"
    },
    {
      "id": "002",
      "quantity": 5,
      "isActive": false,
      "brand": "Life Fitness"
    },
    {
      "id": "003",
      "quantity": 0,
      "isActive": true,
      "brand": "Matrix"
    }
  ],
  "changed_by": "Gestor Academia"
}
```

## 🚨 Tratamento de Erros

### **Códigos de Status HTTP**
- `200`: Sucesso
- `400`: Dados inválidos
- `401`: Não autenticado
- `403`: Sem permissão
- `500`: Erro interno do servidor

### **Mensagens de Erro Comuns**
- `"A configuração 'Permitir criação de treino automatizado (I.A)' está desabilitada"`
- `"O ID do equipamento é obrigatório"`
- `"A quantidade do equipamento '[ID]' deve ser maior ou igual a 0"`
- `"O status ativo do equipamento '[ID]' é obrigatório"`
- `"É necessário informar pelo menos um equipamento com dados válidos"`

## 📝 Checklist para Implementação

### **Antes de Implementar**
- [ ] Verificar se o usuário tem permissão `APARELHOS`
- [ ] Confirmar que a configuração IA está habilitada
- [ ] Obter `empresaId` do contexto do usuário
- [ ] Implementar validações no front-end

### **Durante a Implementação**
- [ ] Validar campos obrigatórios antes do envio
- [ ] Implementar loading state
- [ ] Tratar todos os códigos de erro HTTP
- [ ] Mostrar mensagens de feedback ao usuário
- [ ] Implementar retry em caso de erro de rede

### **Após a Implementação**
- [ ] Testar com dados mínimos
- [ ] Testar com dados completos
- [ ] Testar cenários de erro
- [ ] Validar logs no back-end
- [ ] Confirmar integração com IA

## 🎯 Resumo Executivo

**O que o front-end precisa enviar:**
1. **Header `empresaId`** (obrigatório)
2. **Lista de equipamentos** com pelo menos `id`, `quantity` e `isActive`
3. **Campos opcionais** para enriquecer os dados
4. **Campo `changed_by`** para auditoria (opcional)

**Como deve ser enviado:**
- **Método:** POST
- **Content-Type:** application/json
- **Autenticação:** Conforme padrão do projeto
- **Validação:** Implementar no front-end para melhor UX

**Estrutura dos dados:**
- **Array de objetos** (não mapa simples)
- **IDs únicos** para identificação
- **Campos enum** com valores específicos
- **Flexibilidade** para campos opcionais
```
