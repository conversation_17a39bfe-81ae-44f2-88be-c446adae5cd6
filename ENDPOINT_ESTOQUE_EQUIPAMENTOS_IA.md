# Endpoint para Envio de Estoque de Equipamentos para IA

## Implementação Realizada

Foi implementado um novo endpoint para enviar informações de estoque de equipamentos para a IA, seguindo o padrão existente no projeto e atualizado para a **nova estrutura da API** que usa lista de objetos com informações detalhadas.

### ⚠️ Mudança Importante na Estrutura da API

A API foi atualizada significativamente:

**Versão Anterior (não mais suportada):**
```json
{
  "equipments": {
    "anilha": 10,
    "banco reto": 2
  }
}
```

**Nova Versão (implementada):**
```json
{
  "equipments": [
    {"id": "002", "quantity": 10, "isActive": true},
    {"id": "050", "quantity": 2, "isActive": true}
  ]
}
```

### Arquivos Modificados/Criados:

1. **AparelhoService.java** - Adicionada assinatura do método `enviarEstoqueEquipamentosIa`
2. **AparelhoServiceImpl.java** - Implementado o método de envio de estoque
3. **AparelhoController.java** - Adicionado endpoint REST `/enviar-estoque-ia`
4. **EstoqueEquipamentosIaDTO.java** - Criado DTO para mapear dados de entrada

### Endpoint Implementado

**URL:** `POST /psec/aparelhos/enviar-estoque-ia`

**Headers:**
- `empresaId` (required): ID da empresa
- `Content-Type: application/json`

**Body (JSON):**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 15,
      "isActive": true,
      "brand": "Technogym",
      "editedName": "Anilha Premium Technogym",
      "hasSafetySystem": false,
      "level": "beginner",
      "targetMuscles": ["bíceps", "tríceps", "peitorais"],
      "weightType": "anilhas"
    },
    {
      "id": "050",
      "quantity": 3,
      "isActive": false,
      "adjustable": true,
      "allowsUnilateral": false,
      "brand": "Life Fitness",
      "editedName": "Supino Inclinado Articulado",
      "hasSafetySystem": true,
      "level": "intermediate",
      "mechanism": "articulado",
      "multiFunction": false,
      "requiresInstructor": false,
      "targetMuscles": ["peitorais", "deltoides", "tríceps"],
      "trajectory": "linear",
      "weightType": "peso embutido"
    }
  ],
  "changed_by": "Fausto Alcântara"
}
```

**Resposta de Sucesso (200):**
```json
{
  "data": "Estoque de equipamentos enviado com sucesso para IA!",
  "success": true
}
```

**Resposta de Erro (400/500):**
```json
{
  "success": false,
  "message": "Mensagem de erro específica"
}
```

### Validações Implementadas

1. **Configuração IA:** Verifica se `PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA` está habilitada
2. **Dados obrigatórios:** Valida se ctx e codigoEmpresaZw estão presentes
3. **Equipamentos:** Verifica se há pelo menos um equipamento com dados válidos
4. **Campos obrigatórios por equipamento:**
   - `id`: ID único do equipamento (obrigatório)
   - `quantity`: Quantidade ≥ 0 (obrigatório)
   - `isActive`: Status ativo/inativo (obrigatório)
5. **Campos opcionais:** Todos os demais campos são opcionais e enviados apenas se preenchidos

### Integração com IA

O endpoint faz uma chamada HTTP POST para:
- **URL:** `{urlTreinoIa}/equipment-stock`
- **Headers:**
  - `Content-Type: application/json`
  - `access-token: {accessTokenTreinoIa}`
  - `zw-key: {chaveEmpresa}-{codigoZW}`

### Exemplo de Uso

```bash
curl -X POST "http://localhost:8080/psec/aparelhos/enviar-estoque-ia" \
  -H "Content-Type: application/json" \
  -H "empresaId: 123" \
  -d '{
    "equipments": [
      {
        "id": "002",
        "quantity": 10,
        "isActive": true,
        "brand": "Technogym",
        "editedName": "Anilha Premium",
        "weightType": "anilhas",
        "level": "beginner"
      },
      {
        "id": "050",
        "quantity": 2,
        "isActive": true,
        "brand": "Life Fitness",
        "editedName": "Banco Reto Premium",
        "mechanism": "articulado",
        "level": "intermediate"
      }
    ],
    "changed_by": "João Silva"
  }'
```

### Logs

O sistema gera logs de debug para acompanhar o processo:
- Início do envio
- Sucesso com resposta da IA
- Erros com detalhes específicos

### Tratamento de Erros

- **Configuração desabilitada:** Retorna erro informando que a IA está desabilitada
- **Dados inválidos:** Valida entrada e retorna mensagens específicas
- **Erro na API IA:** Captura e repassa erros da API externa
- **Erro de rede:** Trata problemas de conectividade

### Segurança

- Utiliza permissão `RecursoEnum.APARELHOS`
- Valida sessão do usuário
- Headers de autenticação obrigatórios
- Validação de empresa

### Campos Suportados por Equipamento

**Obrigatórios:**
- `id`: ID único do equipamento
- `quantity`: Quantidade disponível (≥ 0)
- `isActive`: Status ativo/inativo

**Opcionais:**
- `hasSafetySystem`: Possui sistema de segurança
- `editedName`: Nome personalizado do equipamento
- `weightType`: Tipo de peso (anilhas, peso embutido, carga livre, elástico)
- `mechanism`: Mecanismo (alavanca, articulado, cabos e polias)
- `trajectory`: Trajetória (convergente, divergente, linear, arco/circular, livre)
- `level`: Nível de complexidade (beginner, intermediate, advanced)
- `allowsUnilateral`: Permite movimentos unilaterais
- `adjustable`: Equipamento ajustável
- `multiFunction`: Possui dupla função
- `requiresInstructor`: Requer instrutor
- `targetMuscles`: Lista de músculos alvo
- `brand`: Marca do equipamento

### Observações

- **Nova estrutura:** Agora usa lista de objetos ao invés de mapa simples nome→quantidade
- **Identificação por ID:** Equipamentos são identificados por ID único, não por nome
- **Informações detalhadas:** Suporta envio de características técnicas completas
- **Validação robusta:** Valida IDs contra catálogo da IA
- **Flexibilidade:** Campos opcionais permitem diferentes níveis de detalhamento
- O endpoint segue o mesmo padrão do `importarAparelhosIa` existente
- Mantém consistência com a arquitetura do projeto
- Implementa todas as validações necessárias conforme nova especificação da API
- Suporta o campo opcional `changed_by` para auditoria
