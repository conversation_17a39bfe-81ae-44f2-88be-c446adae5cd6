# 🎨 Exemplos de Interface - Estoque de Equipamentos para IA

## 📋 Formulário HTML Básico

### **HTML Structure**
```html
<form id="estoqueEquipamentosForm">
  <div class="form-header">
    <h3>Enviar Estoque de Equipamentos para IA</h3>
    <p>Configure as informações dos equipamentos disponíveis na academia</p>
  </div>
  
  <!-- Campo Changed By -->
  <div class="form-group">
    <label for="changedBy">Responsável pela Alteração (Opcional)</label>
    <input type="text" id="changedBy" name="changedBy" placeholder="Nome do usuário">
  </div>
  
  <!-- Lista de Equipamentos -->
  <div id="equipamentosContainer">
    <h4>Equipamentos</h4>
    <div id="equipamentosList">
      <!-- Equipamentos serão adicionados dinamicamente -->
    </div>
    <button type="button" id="addEquipamento" class="btn btn-secondary">
      + Ad<PERSON><PERSON>r <PERSON>qui<PERSON>
    </button>
  </div>
  
  <!-- Botões de Ação -->
  <div class="form-actions">
    <button type="button" id="cancelar" class="btn btn-cancel">Cancelar</button>
    <button type="submit" id="enviar" class="btn btn-primary">Enviar para IA</button>
  </div>
  
  <!-- Loading e Mensagens -->
  <div id="loading" class="loading hidden">
    <span>Enviando dados para IA...</span>
  </div>
  
  <div id="messages" class="messages"></div>
</form>
```

### **Template de Equipamento**
```html
<div class="equipamento-item" data-index="{index}">
  <div class="equipamento-header">
    <h5>Equipamento #{index + 1}</h5>
    <button type="button" class="btn-remove" onclick="removerEquipamento({index})">×</button>
  </div>
  
  <!-- Campos Obrigatórios -->
  <div class="row">
    <div class="col-md-4">
      <label>ID do Equipamento *</label>
      <input type="text" name="equipamentos[{index}].id" required placeholder="Ex: 002">
    </div>
    <div class="col-md-4">
      <label>Quantidade *</label>
      <input type="number" name="equipamentos[{index}].quantity" min="0" required placeholder="0">
    </div>
    <div class="col-md-4">
      <label>Status *</label>
      <select name="equipamentos[{index}].isActive" required>
        <option value="">Selecione...</option>
        <option value="true">Ativo</option>
        <option value="false">Inativo</option>
      </select>
    </div>
  </div>
  
  <!-- Campos Opcionais - Seção Expansível -->
  <div class="optional-fields">
    <button type="button" class="toggle-optional" onclick="toggleOptionalFields({index})">
      ⚙️ Campos Opcionais
    </button>
    
    <div class="optional-content hidden" id="optional-{index}">
      <div class="row">
        <div class="col-md-6">
          <label>Nome Personalizado</label>
          <input type="text" name="equipamentos[{index}].editedName" placeholder="Ex: Supino Premium">
        </div>
        <div class="col-md-6">
          <label>Marca</label>
          <input type="text" name="equipamentos[{index}].brand" placeholder="Ex: Technogym">
        </div>
      </div>
      
      <div class="row">
        <div class="col-md-4">
          <label>Tipo de Peso</label>
          <select name="equipamentos[{index}].weightType">
            <option value="">Selecione...</option>
            <option value="anilhas">Anilhas</option>
            <option value="peso embutido">Peso Embutido</option>
            <option value="carga livre">Carga Livre</option>
            <option value="elástico">Elástico</option>
          </select>
        </div>
        <div class="col-md-4">
          <label>Mecanismo</label>
          <select name="equipamentos[{index}].mechanism">
            <option value="">Selecione...</option>
            <option value="alavanca">Alavanca</option>
            <option value="articulado">Articulado</option>
            <option value="cabos e polias">Cabos e Polias</option>
          </select>
        </div>
        <div class="col-md-4">
          <label>Nível</label>
          <select name="equipamentos[{index}].level">
            <option value="">Selecione...</option>
            <option value="beginner">Iniciante</option>
            <option value="intermediate">Intermediário</option>
            <option value="advanced">Avançado</option>
          </select>
        </div>
      </div>
      
      <!-- Checkboxes -->
      <div class="checkbox-group">
        <label><input type="checkbox" name="equipamentos[{index}].hasSafetySystem"> Sistema de Segurança</label>
        <label><input type="checkbox" name="equipamentos[{index}].adjustable"> Ajustável</label>
        <label><input type="checkbox" name="equipamentos[{index}].allowsUnilateral"> Movimentos Unilaterais</label>
        <label><input type="checkbox" name="equipamentos[{index}].multiFunction"> Multifunção</label>
        <label><input type="checkbox" name="equipamentos[{index}].requiresInstructor"> Requer Instrutor</label>
      </div>
      
      <!-- Músculos Alvo -->
      <div class="form-group">
        <label>Músculos Alvo</label>
        <div class="muscles-tags">
          <input type="text" placeholder="Digite e pressione Enter" onkeypress="addMuscle(event, {index})">
          <div class="tags-container" id="muscles-{index}"></div>
        </div>
      </div>
    </div>
  </div>
</div>
```

## 💻 JavaScript para Gerenciamento

### **Classe Principal**
```javascript
class EstoqueEquipamentosManager {
  constructor() {
    this.equipamentos = [];
    this.empresaId = this.getEmpresaId();
    this.init();
  }
  
  init() {
    this.bindEvents();
    this.addEquipamento(); // Adiciona primeiro equipamento
  }
  
  bindEvents() {
    document.getElementById('addEquipamento').addEventListener('click', () => this.addEquipamento());
    document.getElementById('estoqueEquipamentosForm').addEventListener('submit', (e) => this.handleSubmit(e));
  }
  
  addEquipamento() {
    const index = this.equipamentos.length;
    const template = this.getEquipamentoTemplate(index);
    
    document.getElementById('equipamentosList').insertAdjacentHTML('beforeend', template);
    
    this.equipamentos.push({
      id: '',
      quantity: 0,
      isActive: null,
      muscles: []
    });
  }
  
  removerEquipamento(index) {
    if (this.equipamentos.length <= 1) {
      alert('É necessário ter pelo menos um equipamento');
      return;
    }
    
    document.querySelector(`[data-index="${index}"]`).remove();
    this.equipamentos.splice(index, 1);
    this.reindexEquipamentos();
  }
  
  toggleOptionalFields(index) {
    const content = document.getElementById(`optional-${index}`);
    content.classList.toggle('hidden');
  }
  
  addMuscle(event, index) {
    if (event.key === 'Enter') {
      event.preventDefault();
      const input = event.target;
      const muscle = input.value.trim();
      
      if (muscle && !this.equipamentos[index].muscles.includes(muscle)) {
        this.equipamentos[index].muscles.push(muscle);
        this.renderMuscles(index);
        input.value = '';
      }
    }
  }
  
  removeMuscle(index, muscle) {
    const muscles = this.equipamentos[index].muscles;
    const muscleIndex = muscles.indexOf(muscle);
    if (muscleIndex > -1) {
      muscles.splice(muscleIndex, 1);
      this.renderMuscles(index);
    }
  }
  
  renderMuscles(index) {
    const container = document.getElementById(`muscles-${index}`);
    const muscles = this.equipamentos[index].muscles;
    
    container.innerHTML = muscles.map(muscle => 
      `<span class="tag">${muscle} <button type="button" onclick="manager.removeMuscle(${index}, '${muscle}')">×</button></span>`
    ).join('');
  }
  
  collectFormData() {
    const formData = new FormData(document.getElementById('estoqueEquipamentosForm'));
    const equipamentos = [];
    
    for (let i = 0; i < this.equipamentos.length; i++) {
      const equipment = {
        id: formData.get(`equipamentos[${i}].id`),
        quantity: parseInt(formData.get(`equipamentos[${i}].quantity`)) || 0,
        isActive: formData.get(`equipamentos[${i}].isActive`) === 'true'
      };
      
      // Campos opcionais
      const optionalFields = [
        'editedName', 'brand', 'weightType', 'mechanism', 'level'
      ];
      
      optionalFields.forEach(field => {
        const value = formData.get(`equipamentos[${i}].${field}`);
        if (value) equipment[field] = value;
      });
      
      // Checkboxes
      const booleanFields = [
        'hasSafetySystem', 'adjustable', 'allowsUnilateral', 
        'multiFunction', 'requiresInstructor'
      ];
      
      booleanFields.forEach(field => {
        const value = formData.get(`equipamentos[${i}].${field}`);
        if (value) equipment[field] = true;
      });
      
      // Músculos
      if (this.equipamentos[i].muscles.length > 0) {
        equipment.targetMuscles = this.equipamentos[i].muscles;
      }
      
      equipamentos.push(equipment);
    }
    
    return {
      equipments: equipamentos,
      changed_by: formData.get('changedBy') || null
    };
  }
  
  async handleSubmit(event) {
    event.preventDefault();
    
    try {
      this.showLoading(true);
      this.clearMessages();
      
      const data = this.collectFormData();
      const errors = this.validateData(data);
      
      if (errors.length > 0) {
        this.showErrors(errors);
        return;
      }
      
      const result = await this.enviarDados(data);
      
      if (result.success) {
        this.showSuccess('Estoque enviado com sucesso para IA!');
        this.resetForm();
      } else {
        this.showError(result.error);
      }
      
    } catch (error) {
      this.showError('Erro inesperado: ' + error.message);
    } finally {
      this.showLoading(false);
    }
  }
  
  validateData(data) {
    const errors = [];
    
    if (!data.equipments || data.equipments.length === 0) {
      errors.push('É necessário adicionar pelo menos um equipamento');
    }
    
    data.equipments.forEach((equipment, index) => {
      if (!equipment.id) {
        errors.push(`Equipamento ${index + 1}: ID é obrigatório`);
      }
      
      if (equipment.quantity < 0) {
        errors.push(`Equipamento ${index + 1}: Quantidade deve ser maior ou igual a 0`);
      }
      
      if (equipment.isActive === null || equipment.isActive === undefined) {
        errors.push(`Equipamento ${index + 1}: Status é obrigatório`);
      }
    });
    
    return errors;
  }
  
  async enviarDados(data) {
    const response = await fetch('/psec/aparelhos/enviar-estoque-ia', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'empresaId': this.empresaId,
        'Authorization': `Bearer ${this.getAuthToken()}`
      },
      body: JSON.stringify(data)
    });
    
    const result = await response.json();
    
    if (response.ok) {
      return { success: true, data: result.data };
    } else {
      return { success: false, error: result.message };
    }
  }
  
  // Métodos utilitários
  showLoading(show) {
    document.getElementById('loading').classList.toggle('hidden', !show);
    document.getElementById('enviar').disabled = show;
  }
  
  clearMessages() {
    document.getElementById('messages').innerHTML = '';
  }
  
  showSuccess(message) {
    document.getElementById('messages').innerHTML = 
      `<div class="alert alert-success">${message}</div>`;
  }
  
  showError(message) {
    document.getElementById('messages').innerHTML = 
      `<div class="alert alert-danger">${message}</div>`;
  }
  
  showErrors(errors) {
    const errorList = errors.map(error => `<li>${error}</li>`).join('');
    document.getElementById('messages').innerHTML = 
      `<div class="alert alert-danger"><ul>${errorList}</ul></div>`;
  }
  
  getEmpresaId() {
    // Implementar conforme o padrão do projeto
    return window.empresaAtual || localStorage.getItem('empresaId');
  }
  
  getAuthToken() {
    // Implementar conforme o padrão do projeto
    return localStorage.getItem('authToken') || window.authToken;
  }
  
  resetForm() {
    document.getElementById('estoqueEquipamentosForm').reset();
    this.equipamentos = [];
    document.getElementById('equipamentosList').innerHTML = '';
    this.addEquipamento();
  }
}

// Inicializar
const manager = new EstoqueEquipamentosManager();
```

## 🎨 CSS Sugerido

### **Estilos Básicos**
```css
.equipamento-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background: #f9f9f9;
}

.equipamento-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.btn-remove {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
}

.optional-fields {
  margin-top: 15px;
}

.toggle-optional {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
}

.optional-content {
  margin-top: 15px;
  padding: 15px;
  background: white;
  border-radius: 4px;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 15px 0;
}

.muscles-tags input {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.tags-container {
  margin-top: 10px;
}

.tag {
  display: inline-block;
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 2px;
  font-size: 12px;
}

.tag button {
  background: none;
  border: none;
  color: white;
  margin-left: 5px;
  cursor: pointer;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.hidden {
  display: none;
}

.alert {
  padding: 15px;
  margin: 15px 0;
  border-radius: 4px;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}
```
