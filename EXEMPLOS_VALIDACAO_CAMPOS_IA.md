# 🧪 Exemplos de Validação - Campos do Endpoint de Estoque IA

## 🎯 Objetivo

Fornecer exemplos práticos de validação e casos de uso específicos para os campos do endpoint de estoque de equipamentos para IA.

## ✅ Casos de Sucesso

### **Caso 1: Equipamento Ativo com Estoque**
```json
{
  "equipments": [
    {
      "id": "001",
      "quantity": 25,
      "isActive": true,
      "name": "Anilha 20kg",
      "brand": "Technogym"
    }
  ],
  "changed_by": "Gerente Academia"
}
```
**Status:** ✅ Válido - Todos os campos obrigatórios preenchidos

### **Caso 2: Equipamento Inativo (Manutenção)**
```json
{
  "equipments": [
    {
      "id": "050",
      "quantity": 1,
      "isActive": false,
      "name": "Esteira Ergométrica",
      "brand": "Matrix",
      "editedName": "Esteira Principal - Em Manutenção"
    }
  ],
  "changed_by": "Técnico Manutenção"
}
```
**Status:** ✅ Válido - Equipamento inativo mas com quantidade registrada

### **Caso 3: Equipamento Sem Estoque**
```json
{
  "equipments": [
    {
      "id": "075",
      "quantity": 0,
      "isActive": true,
      "name": "Kettlebell 16kg",
      "brand": "Rogue"
    }
  ]
}
```
**Status:** ✅ Válido - Quantidade zero é permitida

### **Caso 4: Equipamento Completo**
```json
{
  "equipments": [
    {
      "id": "100",
      "quantity": 3,
      "isActive": true,
      "name": "Supino Inclinado",
      "type": "MUSCULACAO",
      "brand": "Life Fitness",
      "editedName": "Supino Inclinado Signature Series",
      "weightType": "peso embutido",
      "mechanism": "articulado",
      "trajectory": "linear",
      "level": "intermediate",
      "hasSafetySystem": true,
      "adjustable": true,
      "allowsUnilateral": false,
      "multiFunction": false,
      "requiresInstructor": false,
      "targetMuscles": ["peitorais", "deltoides anterior", "tríceps"]
    }
  ],
  "changed_by": "Instrutor Chefe"
}
```
**Status:** ✅ Válido - Exemplo com todos os campos preenchidos

## ❌ Casos de Erro

### **Erro 1: ID Ausente**
```json
{
  "equipments": [
    {
      "quantity": 10,
      "isActive": true,
      "name": "Equipamento Teste"
    }
  ]
}
```
**Erro:** `"O ID do equipamento é obrigatório"`

### **Erro 2: ID Vazio**
```json
{
  "equipments": [
    {
      "id": "",
      "quantity": 10,
      "isActive": true
    }
  ]
}
```
**Erro:** `"O ID do equipamento é obrigatório"`

### **Erro 3: Quantidade Negativa**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": -5,
      "isActive": true
    }
  ]
}
```
**Erro:** `"A quantidade do equipamento '002' deve ser maior ou igual a 0"`

### **Erro 4: Quantidade Ausente**
```json
{
  "equipments": [
    {
      "id": "002",
      "isActive": true,
      "name": "Teste"
    }
  ]
}
```
**Erro:** `"A quantidade do equipamento '002' deve ser maior ou igual a 0"`

### **Erro 5: Status Ausente**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 10,
      "name": "Teste"
    }
  ]
}
```
**Erro:** `"O status ativo do equipamento '002' é obrigatório"`

### **Erro 6: Lista Vazia**
```json
{
  "equipments": [],
  "changed_by": "Usuario"
}
```
**Erro:** `"Os dados de equipamentos são obrigatórios e não podem estar vazios"`

### **Erro 7: Equipments Ausente**
```json
{
  "changed_by": "Usuario"
}
```
**Erro:** `"Os dados de equipamentos são obrigatórios e não podem estar vazios"`

## 🔧 Validações de Campos Enum

### **weightType - Valores Válidos**
```json
// ✅ Válidos
"weightType": "anilhas"
"weightType": "peso embutido"
"weightType": "carga livre"
"weightType": "elástico"

// ❌ Inválidos
"weightType": "ANILHAS"        // Case sensitive
"weightType": "peso_embutido"  // Underscore não permitido
"weightType": "livre"          // Valor incompleto
```

### **mechanism - Valores Válidos**
```json
// ✅ Válidos
"mechanism": "alavanca"
"mechanism": "articulado"
"mechanism": "cabos e polias"

// ❌ Inválidos
"mechanism": "Alavanca"        // Case sensitive
"mechanism": "cabos_polias"    // Formato incorreto
"mechanism": "hidraulico"      // Valor não suportado
```

### **trajectory - Valores Válidos**
```json
// ✅ Válidos
"trajectory": "convergente"
"trajectory": "divergente"
"trajectory": "linear"
"trajectory": "arco/circular"
"trajectory": "livre"

// ❌ Inválidos
"trajectory": "Linear"         // Case sensitive
"trajectory": "arco"           // Valor incompleto
"trajectory": "circular"       // Valor incompleto
```

### **level - Valores Válidos**
```json
// ✅ Válidos
"level": "beginner"
"level": "intermediate"
"level": "advanced"

// ❌ Inválidos
"level": "iniciante"           // Deve ser em inglês
"level": "Beginner"            // Case sensitive
"level": "expert"              // Valor não suportado
```

## 📋 Casos de Uso Específicos

### **Caso de Uso 1: Academia Pequena (Básico)**
```json
{
  "equipments": [
    {
      "id": "001",
      "quantity": 10,
      "isActive": true,
      "name": "Anilha"
    },
    {
      "id": "002",
      "quantity": 2,
      "isActive": true,
      "name": "Banco"
    },
    {
      "id": "003",
      "quantity": 1,
      "isActive": true,
      "name": "Barra"
    }
  ],
  "changed_by": "Proprietário"
}
```

### **Caso de Uso 2: Academia Premium (Detalhado)**
```json
{
  "equipments": [
    {
      "id": "001",
      "quantity": 50,
      "isActive": true,
      "name": "Anilha Olímpica",
      "type": "PESO_LIVRE",
      "brand": "Eleiko",
      "weightType": "anilhas",
      "level": "advanced",
      "targetMuscles": ["todos os grupos musculares"]
    },
    {
      "id": "050",
      "quantity": 5,
      "isActive": true,
      "name": "Leg Press 45°",
      "type": "MUSCULACAO",
      "brand": "Technogym",
      "mechanism": "alavanca",
      "trajectory": "linear",
      "level": "intermediate",
      "hasSafetySystem": true,
      "adjustable": true,
      "targetMuscles": ["quadríceps", "glúteos", "panturrilha"]
    }
  ],
  "changed_by": "Gerente Técnico"
}
```

### **Caso de Uso 3: Atualização de Manutenção**
```json
{
  "equipments": [
    {
      "id": "025",
      "quantity": 1,
      "isActive": false,
      "name": "Esteira Profissional",
      "editedName": "Esteira 1 - Aguardando Peça"
    },
    {
      "id": "026",
      "quantity": 1,
      "isActive": true,
      "name": "Esteira Profissional",
      "editedName": "Esteira 2 - Funcionando"
    }
  ],
  "changed_by": "Equipe Manutenção"
}
```

## 🔍 Dicas de Implementação

### **Validação no Frontend (Recomendada)**
```javascript
function validarEquipamento(equipment) {
  const erros = [];
  
  // Campos obrigatórios
  if (!equipment.id || equipment.id.trim() === '') {
    erros.push('ID é obrigatório');
  }
  
  if (equipment.quantity === null || equipment.quantity === undefined) {
    erros.push('Quantidade é obrigatória');
  } else if (equipment.quantity < 0) {
    erros.push('Quantidade deve ser maior ou igual a 0');
  }
  
  if (equipment.isActive === null || equipment.isActive === undefined) {
    erros.push('Status ativo é obrigatório');
  }
  
  // Validações de enum
  const weightTypes = ['anilhas', 'peso embutido', 'carga livre', 'elástico'];
  if (equipment.weightType && !weightTypes.includes(equipment.weightType)) {
    erros.push('Tipo de peso inválido');
  }
  
  return erros;
}
```

### **Formatação de ID**
```javascript
// O backend espera IDs numéricos como string
// Exemplos corretos:
"id": "1"     // Será formatado para "001"
"id": "15"    // Será formatado para "015"
"id": "123"   // Permanece "123"

// Evitar:
"id": "001"   // Funciona, mas desnecessário
"id": "ABC"   // Erro - deve ser numérico
```

### **Tratamento de Campos Opcionais**
```javascript
// Enviar apenas campos com valores válidos
const equipment = {
  id: "002",
  quantity: 10,
  isActive: true
};

// Adicionar campos opcionais apenas se preenchidos
if (name && name.trim()) {
  equipment.name = name.trim();
}

if (brand && brand.trim()) {
  equipment.brand = brand.trim();
}

// Não enviar campos vazios ou null
```

## 📊 Resumo de Validações

### **Validações Automáticas do Backend**
- ✅ Lista não pode estar vazia
- ✅ ID não pode ser nulo/vazio
- ✅ Quantidade deve ser ≥ 0
- ✅ Status ativo deve ser boolean
- ✅ Configuração IA deve estar habilitada
- ✅ Usuário deve ter permissão

### **Validações Recomendadas no Frontend**
- ✅ Validar campos obrigatórios antes do envio
- ✅ Verificar valores de campos enum
- ✅ Limpar espaços em branco
- ✅ Converter tipos quando necessário
- ✅ Mostrar mensagens de erro claras

### **Boas Práticas**
- ✅ Sempre incluir `name` para identificação
- ✅ Usar `changed_by` para auditoria
- ✅ Preencher `brand` quando disponível
- ✅ Usar campos técnicos para equipamentos especializados
- ✅ Incluir `targetMuscles` para equipamentos de musculação
