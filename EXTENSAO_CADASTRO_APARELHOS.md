# Extensão do Cadastro de Aparelhos - TW-2592

## Resumo
Este documento detalha a implementação da extensão do cadastro de aparelhos no sistema, adicionando novos campos técnicos para melhor caracterização dos equipamentos.

## Novos Campos Implementados

### 1. Enumerações Criadas

#### TipoPesoEnum
- **Localização**: `src/main/java/br/com/pacto/bean/aparelho/TipoPesoEnum.java`
- **Valores**:
  - `ANILHAS` - "Anilhas"
  - `PESO_EMBUTIDO` - "Peso embutido"
  - `CARGA_LIVRE` - "Carga livre"
  - `ELASTICO` - "Elástico"

#### MecanismoEnum
- **Localização**: `src/main/java/br/com/pacto/bean/aparelho/MecanismoEnum.java`
- **Valores**:
  - `ALAVANCA` - "Alavanca"
  - `ARTICULADO` - "Articulado"
  - `CABOS_E_POLIAS` - "Cabos e polias"

#### TipoTrajetoriaEnum
- **Localização**: `src/main/java/br/com/pacto/bean/aparelho/TipoTrajetoriaEnum.java`
- **Valores**:
  - `CONVERGENTE` - "Convergente"
  - `DIVERGENTE` - "Divergente"
  - `LINEAR` - "Linear"
  - `ARCO_CIRCULAR` - "Arco/Circular"
  - `LIVRE` - "Livre"

#### NivelComplexidadeEnum
- **Localização**: `src/main/java/br/com/pacto/bean/aparelho/NivelComplexidadeEnum.java`
- **Valores**:
  - `BEGINNER` - "beginner"
  - `INTERMEDIATE` - "intermediate"
  - `ADVANCED` - "advanced"

### 2. Campos Adicionados na Entidade Aparelho

| Campo | Tipo | Descrição | Valor Padrão |
|-------|------|-----------|--------------|
| `quantidade` | `Integer` | Quantidade disponível do aparelho | 1 |
| `ativo` | `Boolean` | Status ativo/inativo do aparelho | true |
| `imageDataUpload` | `String` (TEXT) | Dados da imagem para upload | null |
| `idEquipmentIa` | `Integer` | ID do equipamento na IA | null |
| `nameEquipmentIa` | `String` | Nome do equipamento na IA | null |
| `isRequiredIa` | `Boolean` | Requerido pela IA | false |
| `tipoPeso` | `TipoPesoEnum` | Tipo de peso do aparelho | null |
| `mecanismo` | `MecanismoEnum` | Mecanismo do aparelho | null |
| `tipoTrajetoria` | `TipoTrajetoriaEnum` | Tipo de trajetória do movimento | null |
| `nivelComplexidade` | `NivelComplexidadeEnum` | Nível de complexidade | null |
| `musculosAlvos` | `String` (TEXT) | Lista de músculos alvos | null |
| `marcaEquipamento` | `String` | Marca do equipamento | null |
| `requerInstrutor` | `Boolean` | Requer instrutor | false |
| `multifuncao` | `Boolean` | Multifunção | false |
| `equipamentoAjustavel` | `Boolean` | Equipamento ajustável | false |
| `permiteMovimentosUnilaterais` | `Boolean` | Permite movimentos unilaterais | false |
| `possuiTravaSeguranca` | `Boolean` | Possui trava de segurança | false |

## Arquivos Modificados

### 1. Entidade Principal
- **`src/main/java/br/com/pacto/bean/aparelho/Aparelho.java`**
  - Adicionados 17 novos campos com anotações JPA apropriadas
  - Implementados getters e setters com validação de nulos para campos booleanos
  - Campos enum mapeados com `@Enumerated(EnumType.STRING)`

### 2. Transfer Objects (TOs)
- **`src/main/java/br/com/pacto/controller/json/programa/AparelhoTO.java`**
  - Adicionados todos os novos campos
  - Implementados getters e setters com validação de nulos
  - Adicionados imports das enumerações

- **`src/main/java/br/com/pacto/controller/json/programa/AparelhoResponseTO.java`**
  - Adicionados todos os novos campos
  - Atualizado construtor para mapear campos da entidade
  - Implementados getters e setters

### 3. Service Layer
- **`src/main/java/br/com/pacto/service/impl/aparelho/AparelhoServiceImpl.java`**
  - Atualizado método `povoarItensAparelho()` para mapear novos campos
  - Mapeamento bidirecional entre AparelhoTO e Aparelho

### 4. Migração de Banco de Dados
- **`src/main/java/br/com/pacto/base/jpa/migrador/Migration_TW_2592.java`**
  - Adicionadas 17 novas colunas na tabela `aparelho`
  - Preservadas colunas existentes
  - Configurados valores padrão apropriados

## Estrutura do Banco de Dados

### Novas Colunas na Tabela `aparelho`

```sql
ALTER TABLE aparelho ADD COLUMN quantidade INTEGER DEFAULT 1 NOT NULL;
ALTER TABLE aparelho ADD COLUMN ativo BOOLEAN DEFAULT TRUE;
ALTER TABLE aparelho ADD COLUMN imageDataUpload TEXT;
ALTER TABLE aparelho ADD COLUMN idEquipmentIa INTEGER;
ALTER TABLE aparelho ADD COLUMN nameEquipmentIa VARCHAR(255);
ALTER TABLE aparelho ADD COLUMN isRequiredIa BOOLEAN DEFAULT FALSE;
ALTER TABLE aparelho ADD COLUMN tipoPeso VARCHAR(50);
ALTER TABLE aparelho ADD COLUMN mecanismo VARCHAR(50);
ALTER TABLE aparelho ADD COLUMN tipoTrajetoria VARCHAR(50);
ALTER TABLE aparelho ADD COLUMN nivelComplexidade VARCHAR(50);
ALTER TABLE aparelho ADD COLUMN musculosAlvos TEXT;
ALTER TABLE aparelho ADD COLUMN marcaEquipamento VARCHAR(255);
ALTER TABLE aparelho ADD COLUMN requerInstrutor BOOLEAN DEFAULT FALSE;
ALTER TABLE aparelho ADD COLUMN multifuncao BOOLEAN DEFAULT FALSE;
ALTER TABLE aparelho ADD COLUMN equipamentoAjustavel BOOLEAN DEFAULT FALSE;
ALTER TABLE aparelho ADD COLUMN permiteMovimentosUnilaterais BOOLEAN DEFAULT FALSE;
ALTER TABLE aparelho ADD COLUMN possuiTravaSeguranca BOOLEAN DEFAULT FALSE;
```

## Resumo dos Campos Adicionados no Migration_TW_2592

### ✅ **Total de 17 campos adicionados:**
1. `quantidade` - INTEGER DEFAULT 1 NOT NULL
2. `ativo` - BOOLEAN DEFAULT TRUE
3. `imageDataUpload` - TEXT
4. `idEquipmentIa` - INTEGER
5. `nameEquipmentIa` - VARCHAR(255)
6. `isRequiredIa` - BOOLEAN DEFAULT FALSE
7. `tipoPeso` - VARCHAR(50)
8. `mecanismo` - VARCHAR(50)
9. `tipoTrajetoria` - VARCHAR(50)
10. `nivelComplexidade` - VARCHAR(50)
11. `musculosAlvos` - TEXT
12. `marcaEquipamento` - VARCHAR(255)
13. `requerInstrutor` - BOOLEAN DEFAULT FALSE
14. `multifuncao` - BOOLEAN DEFAULT FALSE
15. `equipamentoAjustavel` - BOOLEAN DEFAULT FALSE
16. `permiteMovimentosUnilaterais` - BOOLEAN DEFAULT FALSE
17. `possuiTravaSeguranca` - BOOLEAN DEFAULT FALSE

## Conclusão

A extensão do cadastro de aparelhos foi implementada seguindo as melhores práticas do sistema, mantendo compatibilidade total com funcionalidades existentes e preparando a base para futuras evoluções do módulo de equipamentos. Todos os campos do AparelhoTO estão incluídos no Migration_TW_2592.
