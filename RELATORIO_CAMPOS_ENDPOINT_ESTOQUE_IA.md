# 📋 Relatório de Campos - Endpoint de Estoque de Equipamentos para IA

## 🎯 Visão Geral

Este documento especifica **todos os campos obrigatórios e opcionais** que devem ser enviados para o endpoint `/psec/aparelhos/enviar-estoque-ia` do backend.

## 🔗 Informações do Endpoint

**URL:** `POST /psec/aparelhos/enviar-estoque-ia`  
**Content-Type:** `application/json`  
**Headers Obrigatórios:** `empresaId`, `Authorization`

## 📤 Estrutura do Payload Principal

### **Campos do Payload Raiz**

| Campo | Tipo | Obrigatório | Descrição | Exemplo |
|-------|------|-------------|-----------|---------|
| `equipments` | `Array<EquipmentInfo>` | ✅ **SIM** | Lista de equipamentos com informações | `[{...}]` |
| `changed_by` | `String` | ❌ Opcional | Nome do usuário que fez a alteração | `"<PERSON>"` |

### **Validações do Payload Raiz**
- ✅ `equipments` **não pode ser nulo ou vazio**
- ✅ Deve conter **pelo menos 1 equipamento**
- ❌ `changed_by` é opcional (pode ser omitido ou null)

## 🔧 Campos por Equipamento (EquipmentInfo)

### **📌 Campos OBRIGATÓRIOS (3 campos)**

| Campo | Tipo | Validação | Descrição | Exemplo |
|-------|------|-----------|-----------|---------|
| `id` | `String` | ✅ **Obrigatório, não vazio** | ID único do equipamento | `"002"` |
| `quantity` | `Integer` | ✅ **Obrigatório, ≥ 0** | Quantidade disponível | `15` |
| `isActive` | `Boolean` | ✅ **Obrigatório, não null** | Status ativo/inativo | `true` |

### **🔍 Validações dos Campos Obrigatórios**
```java
// Validação do ID
if (UteisValidacao.emptyString(equipment.getId())) {
    throw new ServiceException("O ID do equipamento é obrigatório");
}

// Validação da quantidade
if (equipment.getQuantity() == null || equipment.getQuantity() < 0) {
    throw new ServiceException("A quantidade do equipamento deve ser maior ou igual a 0");
}

// Validação do status ativo
if (equipment.getIsActive() == null) {
    throw new ServiceException("O status ativo do equipamento é obrigatório");
}
```

### **📋 Campos OPCIONAIS (15 campos)**

#### **Informações Básicas**
| Campo | Tipo | Descrição | Exemplo |
|-------|------|-----------|---------|
| `name` | `String` | Nome do equipamento | `"Esteira Profissional"` |
| `type` | `String` | Tipo do equipamento | `"ANILHAS"` |
| `brand` | `String` | Marca do equipamento | `"Technogym"` |
| `editedName` | `String` | Nome personalizado | `"Supino Premium"` |

#### **Características Técnicas**
| Campo | Tipo | Valores Permitidos | Exemplo |
|-------|------|-------------------|---------|
| `weightType` | `String` | `"anilhas"`, `"peso embutido"`, `"carga livre"`, `"elástico"` | `"anilhas"` |
| `mechanism` | `String` | `"alavanca"`, `"articulado"`, `"cabos e polias"` | `"alavanca"` |
| `trajectory` | `String` | `"convergente"`, `"divergente"`, `"linear"`, `"arco/circular"`, `"livre"` | `"linear"` |
| `level` | `String` | `"beginner"`, `"intermediate"`, `"advanced"` | `"intermediate"` |

#### **Características Funcionais**
| Campo | Tipo | Descrição | Exemplo |
|-------|------|-----------|---------|
| `hasSafetySystem` | `Boolean` | Possui sistema de segurança | `true` |
| `adjustable` | `Boolean` | Equipamento é ajustável | `true` |
| `allowsUnilateral` | `Boolean` | Permite movimentos unilaterais | `false` |
| `multiFunction` | `Boolean` | Possui dupla função | `false` |
| `requiresInstructor` | `Boolean` | Requer acompanhamento de instrutor | `true` |

#### **Músculos Alvo**
| Campo | Tipo | Descrição | Exemplo |
|-------|------|-----------|---------|
| `targetMuscles` | `Array<String>` | Lista de músculos trabalhados | `["peitoral", "tríceps", "deltoides"]` |

## 📝 Exemplos Práticos

### **Exemplo 1: Payload Mínimo (Apenas Campos Obrigatórios)**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 10,
      "isActive": true
    }
  ]
}
```

### **Exemplo 2: Payload com Campos Básicos**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 15,
      "isActive": true,
      "name": "Esteira Profissional",
      "type": "CARDIO",
      "brand": "Technogym"
    }
  ],
  "changed_by": "João Silva"
}
```

### **Exemplo 3: Payload Completo (Todos os Campos)**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 15,
      "isActive": true,
      "name": "Supino Inclinado",
      "type": "MUSCULACAO",
      "brand": "Technogym",
      "editedName": "Supino Inclinado Premium",
      "weightType": "peso embutido",
      "mechanism": "articulado",
      "trajectory": "linear",
      "level": "intermediate",
      "hasSafetySystem": true,
      "adjustable": true,
      "allowsUnilateral": false,
      "multiFunction": false,
      "requiresInstructor": false,
      "targetMuscles": ["peitorais", "deltoides", "tríceps"]
    }
  ],
  "changed_by": "Administrador Sistema"
}
```

### **Exemplo 4: Múltiplos Equipamentos**
```json
{
  "equipments": [
    {
      "id": "001",
      "quantity": 20,
      "isActive": true,
      "name": "Anilha",
      "brand": "Technogym"
    },
    {
      "id": "002",
      "quantity": 5,
      "isActive": true,
      "name": "Banco Reto",
      "brand": "Life Fitness"
    },
    {
      "id": "003",
      "quantity": 0,
      "isActive": false,
      "name": "Esteira (Manutenção)",
      "brand": "Matrix"
    }
  ],
  "changed_by": "Gestor Academia"
}
```

## ⚠️ Validações e Regras de Negócio

### **Validações de Entrada**
1. ✅ **Lista não vazia:** Deve conter pelo menos 1 equipamento
2. ✅ **ID obrigatório:** Não pode ser nulo, vazio ou apenas espaços
3. ✅ **Quantidade válida:** Deve ser ≥ 0 (zero é permitido)
4. ✅ **Status definido:** `isActive` deve ser `true` ou `false` (não null)
5. ✅ **Valores enum:** Campos como `weightType`, `mechanism`, etc. devem usar valores válidos

### **Processamento do ID**
```java
// O backend formata o ID para 3 dígitos com zeros à esquerda
int idNumerico = Integer.parseInt(equipment.getId());
String idFormatado = String.format("%03d", idNumerico);
// Exemplo: "2" → "002", "15" → "015"
```

### **Tratamento de Campos Opcionais**
- ❌ Campos `null` ou vazios **não são enviados** para a IA
- ✅ Apenas campos com valores válidos são incluídos no payload
- ✅ Arrays vazios são ignorados

## 🚨 Mensagens de Erro Comuns

### **Erros de Validação**
```json
// Lista vazia
{
  "success": false,
  "message": "Os dados de equipamentos são obrigatórios e não podem estar vazios"
}

// ID ausente
{
  "success": false,
  "message": "O ID do equipamento é obrigatório"
}

// Quantidade inválida
{
  "success": false,
  "message": "A quantidade do equipamento '002' deve ser maior ou igual a 0"
}

// Status não definido
{
  "success": false,
  "message": "O status ativo do equipamento '002' é obrigatório"
}
```

### **Erros de Sistema**
```json
// Configuração IA desabilitada
{
  "success": false,
  "message": "A configuração 'Permitir criação de treino automatizado (I.A)' está desabilitada"
}

// Erro na integração com IA
{
  "success": false,
  "message": "Erro ao enviar estoque de equipamentos para IA: HTTP 400 - IDs inválidos"
}
```

## ✅ Checklist de Implementação

### **Antes de Enviar**
- [ ] Verificar se `equipments` não está vazio
- [ ] Validar que todos os equipamentos têm `id`, `quantity` e `isActive`
- [ ] Confirmar que `quantity` é ≥ 0
- [ ] Verificar valores de campos enum se utilizados
- [ ] Incluir header `empresaId`

### **Campos Obrigatórios por Equipamento**
- [ ] `id` (String, não vazio)
- [ ] `quantity` (Integer, ≥ 0)
- [ ] `isActive` (Boolean, não null)

### **Campos Opcionais Recomendados**
- [ ] `name` - Para identificação visual
- [ ] `brand` - Para informações da marca
- [ ] `type` - Para categorização

## 📊 Resumo Executivo

### **Estrutura Mínima Funcional**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 10,
      "isActive": true
    }
  ]
}
```

### **Campos por Categoria**
- **Obrigatórios:** 3 campos (`id`, `quantity`, `isActive`)
- **Opcionais Básicos:** 4 campos (`name`, `type`, `brand`, `editedName`)
- **Opcionais Técnicos:** 4 campos (`weightType`, `mechanism`, `trajectory`, `level`)
- **Opcionais Funcionais:** 5 campos (características booleanas)
- **Músculos:** 1 campo (`targetMuscles`)
- **Auditoria:** 1 campo global (`changed_by`)

### **Total de Campos Suportados**
- **18 campos** por equipamento (3 obrigatórios + 15 opcionais)
- **1 campo global** opcional (`changed_by`)
- **Flexibilidade total** para diferentes níveis de detalhamento
