# 🧪 Testes de Integração Frontend-Backend - Estoque IA

## 🎯 Objetivo

Validar que a integração entre frontend e backend está funcionando corretamente com os dados reais enviados pelo frontend.

## 📋 Cenários de Teste

### **Teste 1: Payload Mínimo do Frontend**

**Descrição:** Testar com dados mínimos enviados pelo frontend

**Request:**
```bash
curl -X POST "http://localhost:8080/psec/aparelhos/enviar-estoque-ia" \
  -H "Content-Type: application/json" \
  -H "empresaId: 1" \
  -H "Authorization: Bearer [TOKEN]" \
  -d '{
    "equipments": [
      {
        "id": "002",
        "quantity": 10,
        "isActive": true
      }
    ],
    "changed_by": "Sistema"
  }'
```

**Resultado Esperado:**
```json
{
  "success": true,
  "data": "Estoque de equipamentos enviado com sucesso para IA!"
}
```

**Logs Esperados:**
```
#### INICIANDO O ENVIO DE ESTOQUE DE EQUIPAMENTOS PARA IA - CTX: [ctx]
#### TOTAL DE EQUIPAMENTOS A ENVIAR: 1
#### EQUIPAMENTO ADICIONADO AO PAYLOAD - ID: 002, Quantidade: 10, Ativo: true, Nome: null
#### ESTOQUE DE EQUIPAMENTOS ENVIADO COM SUCESSO PARA IA - CTX: [ctx]
```

### **Teste 2: Payload Completo do Frontend**

**Descrição:** Testar com todos os campos enviados pelo frontend

**Request:**
```bash
curl -X POST "http://localhost:8080/psec/aparelhos/enviar-estoque-ia" \
  -H "Content-Type: application/json" \
  -H "empresaId: 1" \
  -H "Authorization: Bearer [TOKEN]" \
  -d '{
    "equipments": [
      {
        "id": "002",
        "quantity": 15,
        "isActive": true,
        "name": "Esteira Profissional",
        "type": "ANILHAS",
        "brand": "TechnoGym"
      }
    ],
    "changed_by": "João Silva"
  }'
```

**Resultado Esperado:**
```json
{
  "success": true,
  "data": "Estoque de equipamentos enviado com sucesso para IA!"
}
```

**Payload para IA Esperado:**
```json
{
  "equipments": [
    {
      "id": "002",
      "quantity": 15,
      "isActive": true,
      "name": "Esteira Profissional",
      "type": "ANILHAS",
      "brand": "TechnoGym"
    }
  ],
  "changed_by": "João Silva"
}
```

### **Teste 3: Múltiplos Equipamentos**

**Descrição:** Testar com múltiplos equipamentos (cenário real do frontend)

**Request:**
```bash
curl -X POST "http://localhost:8080/psec/aparelhos/enviar-estoque-ia" \
  -H "Content-Type: application/json" \
  -H "empresaId: 1" \
  -H "Authorization: Bearer [TOKEN]" \
  -d '{
    "equipments": [
      {
        "id": "002",
        "quantity": 10,
        "isActive": true,
        "name": "Anilha",
        "type": "ANILHAS",
        "brand": "TechnoGym"
      },
      {
        "id": "050",
        "quantity": 0,
        "isActive": false,
        "name": "Supino Inclinado",
        "type": "PESO_EMBUTIDO",
        "brand": "Life Fitness"
      }
    ],
    "changed_by": "Administrador"
  }'
```

### **Teste 4: Validação de Erro - Campos Obrigatórios**

**Descrição:** Testar validação de campos obrigatórios

**Request:**
```bash
curl -X POST "http://localhost:8080/psec/aparelhos/enviar-estoque-ia" \
  -H "Content-Type: application/json" \
  -H "empresaId: 1" \
  -H "Authorization: Bearer [TOKEN]" \
  -d '{
    "equipments": [
      {
        "quantity": 10,
        "isActive": true
      }
    ]
  }'
```

**Resultado Esperado:**
```json
{
  "success": false,
  "message": "O ID do equipamento é obrigatório"
}
```

### **Teste 5: Validação de Erro - Quantidade Inválida**

**Request:**
```bash
curl -X POST "http://localhost:8080/psec/aparelhos/enviar-estoque-ia" \
  -H "Content-Type: application/json" \
  -H "empresaId: 1" \
  -H "Authorization: Bearer [TOKEN]" \
  -d '{
    "equipments": [
      {
        "id": "002",
        "quantity": -5,
        "isActive": true
      }
    ]
  }'
```

**Resultado Esperado:**
```json
{
  "success": false,
  "message": "A quantidade do equipamento '002' deve ser maior ou igual a 0"
}
```

### **Teste 6: Validação de Erro - Lista Vazia**

**Request:**
```bash
curl -X POST "http://localhost:8080/psec/aparelhos/enviar-estoque-ia" \
  -H "Content-Type: application/json" \
  -H "empresaId: 1" \
  -H "Authorization: Bearer [TOKEN]" \
  -d '{
    "equipments": [],
    "changed_by": "Sistema"
  }'
```

**Resultado Esperado:**
```json
{
  "success": false,
  "message": "Os dados de equipamentos são obrigatórios e não podem estar vazios"
}
```

## 🔍 Verificações de Log

### **Logs de Sucesso**
Verificar se aparecem nos logs do servidor:
```
INFO: Iniciando envio de estoque para IA - EmpresaId: 1, Equipamentos: 1, ChangedBy: João Silva
DEBUG: #### INICIANDO O ENVIO DE ESTOQUE DE EQUIPAMENTOS PARA IA - CTX: [ctx]
DEBUG: #### TOTAL DE EQUIPAMENTOS A ENVIAR: 1
DEBUG: #### URL DA IA: [url]/equipment-stock
DEBUG: #### ZW-KEY: [ctx]-1
DEBUG: #### EQUIPAMENTO ADICIONADO AO PAYLOAD - ID: 002, Quantidade: 15, Ativo: true, Nome: Esteira Profissional
DEBUG: #### PAYLOAD FINAL PARA IA: {"equipments":[{"id":"002","quantity":15,"isActive":true,"name":"Esteira Profissional","type":"ANILHAS","brand":"TechnoGym"}],"changed_by":"João Silva"}
DEBUG: #### ENVIANDO REQUISIÇÃO PARA IA...
DEBUG: #### RESPOSTA DA IA - Status: 200, Body: {"message":"Equipamentos registrados","count":1}
DEBUG: #### ESTOQUE DE EQUIPAMENTOS ENVIADO COM SUCESSO PARA IA - CTX: [ctx]
INFO: Estoque enviado com sucesso para IA - EmpresaId: 1
```

### **Logs de Erro**
Verificar se aparecem nos logs em caso de erro:
```
SEVERE: Erro ao tentar enviar estoque de equipamentos para IA - EmpresaId: 1
DEBUG: #### ERRO NA RESPOSTA DA IA: Erro ao enviar estoque de equipamentos para IA: HTTP 400 - {"detail":"IDs de equipamentos inválidos: ['999']"}
DEBUG: #### ERRO AO ENVIAR ESTOQUE DE EQUIPAMENTOS PARA IA - CTX: [ctx] - Erro: Erro ao enviar estoque de equipamentos para IA: HTTP 400 - {"detail":"IDs de equipamentos inválidos: ['999']"}
```

## 🛠️ Ferramentas de Debug

### **Verificar Configuração IA**
```sql
SELECT * FROM configuracao_sistema 
WHERE tipo = 'PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA';
```

### **Verificar Logs em Tempo Real**
```bash
tail -f logs/application.log | grep "ESTOQUE\|IA\|equipment"
```

### **Verificar Propriedades da IA**
```bash
grep -i "ia\|treino" src/main/resources/br/com/pacto/util/resources/OpcoesGlobais.properties
```

## ✅ Checklist de Validação

### **Funcionalidades Básicas**
- [ ] Endpoint responde corretamente
- [ ] Headers são processados adequadamente
- [ ] Payload é deserializado corretamente
- [ ] Validações funcionam como esperado

### **Integração com IA**
- [ ] Payload é montado corretamente
- [ ] Headers da IA são enviados
- [ ] Resposta da IA é processada
- [ ] Erros da IA são tratados

### **Logs e Debugging**
- [ ] Logs de início aparecem
- [ ] Logs de equipamentos aparecem
- [ ] Logs de payload aparecem
- [ ] Logs de resposta aparecem
- [ ] Logs de erro aparecem quando necessário

### **Campos do Frontend**
- [ ] Campo `name` é mapeado
- [ ] Campo `type` é mapeado
- [ ] Campo `brand` é mapeado
- [ ] Campos opcionais funcionam
- [ ] Campos obrigatórios são validados

## 🚨 Problemas Comuns e Soluções

### **Erro: "Configuração IA desabilitada"**
**Solução:** Habilitar configuração no banco:
```sql
UPDATE configuracao_sistema 
SET valor = 'true' 
WHERE tipo = 'PERMITIR_CRIAR_TREINO_AUTOMATIZADO_IA';
```

### **Erro: "Token de acesso inválido"**
**Solução:** Verificar propriedade `accessTokenTreinoIa` no arquivo de configuração

### **Erro: "URL não encontrada"**
**Solução:** Verificar propriedade `urlTreinoIa` no arquivo de configuração

### **Erro: "EmpresaId não informado"**
**Solução:** Verificar se header `empresaId` está sendo enviado pelo frontend

## 📊 Métricas de Sucesso

- ✅ **Taxa de Sucesso:** > 95% dos requests bem-sucedidos
- ✅ **Tempo de Resposta:** < 5 segundos
- ✅ **Logs Completos:** Todos os logs aparecem corretamente
- ✅ **Validações:** Todas as validações funcionam
- ✅ **Compatibilidade:** 100% compatível com frontend
