# Análise: Fluxo de Logo na Impressão de Avaliação Física para Treino Independente

## Resumo Executivo

Este relatório analisa o fluxo completo de como o logo da empresa é processado e exibido na impressão de avaliações físicas, com foco específico no problema identificado para treino independente onde o logo personalizado não está sendo utilizado.

## 1. Endpoint de Impressão

### 1.1 URL Analisada
```
GET http://host.docker.internal:8201/TreinoWeb/prest/psec/avaliacoes-fisica/958/imprimir?idiomaBanco=0
```

### 1.2 Controller - AvaliacaoFisicaController
**Arquivo**: `src/main/java/br/com/pacto/controller/json/avaliacao/AvaliacaoFisicaController.java`
**Método**: `imprimir()` - Linhas 175-186

```java
@RequestMapping(value = "/{id}/imprimir", method = RequestMethod.GET)
public ResponseEntity<EnvelopeRespostaDTO> imprimir(@PathVariable("id") final Integer id,
                                                    @RequestParam(value = "idiomaBanco", required = false) final String idiomaBanco, 
                                                    HttpServletRequest request) {
    String ctx = sessaoService.getUsuarioAtual().getChave();
    String language = UteisValidacao.emptyString(idiomaBanco) ? "PT" : IdiomaBancoEnum.getFromOrdinal(Integer.valueOf(idiomaBanco)).name();
    return ResponseEntityFactory.ok(avaliacaoFisicaService.comporUrlPdf(ctx, id, request, true, language));
}
```

## 2. Fluxo de Geração do PDF

### 2.1 Método comporUrlPdf()
**Arquivo**: `src/main/java/br/com/pacto/service/impl/avaliacao/AvaliacaoFisicaImpl.java`
**Linhas**: 6343-6396

**Fluxo**:
1. Obtém dados da avaliação física
2. Carrega dados complementares (flexibilidade, peso ósseo, ventilometria, anamnese)
3. Chama `gerarPDFAvaliacaoFisica()` para gerar o PDF

### 2.2 Método gerarPDFAvaliacaoFisica()
**Linhas**: 2358-2396

**Fluxo**:
1. Cria instância do `PDFFromXmlFile`
2. Chama `prepareParamsImpressao()` para preparar parâmetros
3. Gera PDF baseado no tipo de treino (independente vs ZW)

## 3. Problema Identificado - Processamento do Logo

### 3.1 Método prepareParamsImpressao()
**Linhas**: 2630-2750

### 3.2 Lógica Problemática para Treino Independente
**Linhas**: 2674-2684

```java
if (SuperControle.independente(key) && usuario == null) {
    List<Empresa> empresaWSIndepedente = empresaService.obterTodos(key);
    for (Empresa ws : empresaWSIndepedente) {
        if (ws.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
            final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
            parameters.put("logoPadraoRelatorio", imagemTreino);
            parameters.put("empresaNome", "");
            parameters.put("empresaEndereco", "");
            parameters.put("empresaSite", "");
        }
    }
}
```

### 3.3 **PROBLEMA**: Logo Padrão em Vez do Logo da Empresa
❌ **Comportamento Atual**: Sistema usa `pacto_home.png` (logo padrão)
✅ **Comportamento Esperado**: Sistema deveria usar `keyImgEmpresa` da empresa

## 4. Lógica Correta para Outros Cenários

### 4.1 Para Treino ZW (Funcionando Corretamente)
**Linhas**: 2694-2705

```java
if (usuario == null) {
    List<EmpresaWS> empresaWS = UtilContext.getBean(AdmWSConsumer.class).obterEmpresas(key);
    for (EmpresaWS ws : empresaWS) {
        if (ws.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
            String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, ws.getCodigo().toString());
            String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
            parameters.put("logoPadraoRelatorio", urlFoto);
            // ... outros parâmetros
        }
    }
}
```

### 4.2 Para Usuário com Empresa Padrão (Funcionando Corretamente)
**Linhas**: 2747-2749

```java
String genKey = MidiaService.getInstance().genKey(key, MidiaEntidadeEnum.FOTO_EMPRESA_RELATORIO, codigoEmpresa.toString());
String urlFoto = Aplicacao.obterUrlFotoDaNuvem(genKey);
parameters.put("logoPadraoRelatorio", urlFoto);
```

## 5. Estrutura de Dados da Empresa

### 5.1 Entidade Empresa
**Arquivo**: `src/main/java/br/com/pacto/bean/empresa/Empresa.java`
**Campo**: `keyImgEmpresa` (String) - Linha 44

### 5.2 DTO de Empresa
**Arquivo**: `src/main/java/br/com/pacto/controller/json/empresa/EmpresaDTO.java`
**Campo**: `keyImgEmpresa` (String) - Linha 11

## 6. Sistema de Mídia

### 6.1 MidiaEntidadeEnum
**Arquivo**: `src/main/java/servicos/integracao/midias/commons/MidiaEntidadeEnum.java`

**Tipos Relevantes**:
- `FOTO_EMPRESA(1, ".jpg", "foto")` - Linha 14
- `FOTO_EMPRESA_RELATORIO(2, ".jpg", "fotoRelatorio")` - Linha 15

### 6.2 MidiaService
**Arquivo**: `src/main/java/servicos/integracao/midias/MidiaService.java`

**Métodos Relevantes**:
- `genKey()` - Gera chave única para mídia
- `getInstanceWood()` - Instância para AWS S3
- `uploadObjectFromByteArray()` - Upload de imagem

### 6.3 Aplicacao.obterUrlFotoDaNuvem()
**Arquivo**: `src/main/java/br/com/pacto/objeto/Aplicacao.java`
**Linhas**: 707-720

**Funcionalidade**:
- Gera URL completa para CDN (`cdn1.pactorian.net`)
- Fallback para `fotoPadrao.jpg` se chave vazia
- Suporte a cache com timestamp

## 7. Detecção de Treino Independente

### 7.1 Método SuperControle.independente()
**Arquivo**: `src/main/java/br/com/pacto/controller/json/base/SuperControle.java`
**Linhas**: 238-246

```java
public static boolean independente(final String ctx) {
    try {
        String mod = Aplicacao.getProp(ctx, Aplicacao.modulos);
        return mod != null && !mod.contains("ZW");
    } catch (Exception e) {
        return false;
    }
}
```

## 8. Correção Proposta

### 8.1 Problema Raiz
O código para treino independente não está utilizando o `keyImgEmpresa` da entidade `Empresa`, usando sempre o logo padrão.

### 8.2 Solução Recomendada
Modificar o bloco de código nas linhas 2674-2684 para:

```java
if (SuperControle.independente(key) && usuario == null) {
    List<Empresa> empresaWSIndepedente = empresaService.obterTodos(key);
    for (Empresa ws : empresaWSIndepedente) {
        if (ws.getCodigo().equals(avaliacaoFisica.getCliente().getEmpresa())) {
            // CORREÇÃO: Usar keyImgEmpresa da empresa
            if (!UteisValidacao.emptyString(ws.getKeyImgEmpresa())) {
                String urlFoto = Aplicacao.obterUrlFotoDaNuvem(ws.getKeyImgEmpresa());
                parameters.put("logoPadraoRelatorio", urlFoto);
            } else {
                // Fallback para logo padrão apenas se não houver keyImgEmpresa
                final String imagemTreino = servletContext.getRealPath("resources") + File.separator + "imagens" + File.separator + "pacto_home.png";
                parameters.put("logoPadraoRelatorio", imagemTreino);
            }
            parameters.put("empresaNome", ws.getNome());
            parameters.put("empresaEndereco", ""); // Empresa não tem endereço
            parameters.put("empresaSite", ""); // Empresa não tem site
        }
    }
}
```

## 9. Fluxo Completo Corrigido

### 9.1 Busca de Dados
1. **Request**: `GET /psec/avaliacoes-fisica/{id}/imprimir`
2. **Controller**: `AvaliacaoFisicaController.imprimir()`
3. **Service**: `AvaliacaoFisicaService.comporUrlPdf()`

### 9.2 Geração de PDF
1. **Preparação**: `prepareParamsImpressao()`
2. **Detecção**: `SuperControle.independente()` verifica tipo de treino
3. **Logo**: 
   - ✅ **ZW**: Usa `MidiaService.genKey()` + `Aplicacao.obterUrlFotoDaNuvem()`
   - ❌ **Independente**: Usa logo padrão (PROBLEMA)
   - ✅ **Independente Corrigido**: Usa `keyImgEmpresa` + `Aplicacao.obterUrlFotoDaNuvem()`

### 9.3 Renderização
1. **PDF**: `PDFFromXmlFile.gerarPDF()`
2. **Template**: Usa parâmetro `logoPadraoRelatorio`
3. **Output**: URL do PDF gerado

## 10. Considerações Técnicas

### 10.1 Compatibilidade
- Solução mantém compatibilidade com treino ZW
- Fallback para logo padrão se `keyImgEmpresa` vazio
- Não afeta outros fluxos de impressão

### 10.2 Performance
- Não adiciona overhead significativo
- Reutiliza infraestrutura existente de CDN
- Cache automático via `Aplicacao.obterUrlFotoDaNuvem()`

### 10.3 Segurança
- Usa mesma validação de outros fluxos
- Mantém controle de acesso existente
- Não expõe dados sensíveis

## Conclusão

O problema está localizado especificamente no tratamento de logos para treino independente. A correção é simples e direta: utilizar o `keyImgEmpresa` da entidade `Empresa` em vez de sempre usar o logo padrão. Esta mudança alinhará o comportamento do treino independente com o treino ZW, garantindo que o logo personalizado da empresa seja exibido corretamente nas impressões de avaliação física.
