# Documentação: Sistema de Imagens para Aparelhos

## Visão Geral

Este documento descreve o funcionamento completo do sistema de imagens para aparelhos, incluindo os endpoints de salvamento e consulta, campos aceitos e retornados, e o fluxo de processamento de imagens.

## Endpoints

### 1. <PERSON><PERSON><PERSON>
- **URL**: `POST /psec/aparelhos`
- **Descrição**: Cria um novo aparelho
- **Body**: `AparelhoTO`
- **Retorno**: `AparelhoResponseTO`

### 2. Alterar Aparelho
- **URL**: `PUT /psec/aparelhos/{id}`
- **Descrição**: Altera um aparelho existente
- **Body**: `AparelhoTO`
- **Retorno**: `AparelhoResponseTO`

### 3. Consultar Aparelho
- **URL**: `GET /psec/aparelhos/{id}`
- **Descrição**: Consulta um aparelho específico
- **Retorno**: `AparelhoResponseTO`

### 4. Listar Todos os Aparelhos
- **URL**: `GET /psec/aparelhos/all?crossfit={boolean}`
- **Descrição**: Lista todos os aparelhos ativos
- **Ordenação**: Por nome (crescente)
- **Retorno**: `List<AtividadeAparelhoResponseRecursiveTO>`

### 5. Listar Aparelhos com Filtros
- **URL**: `GET /psec/aparelhos?filters={json}&page={int}&size={int}&sort={string}`
- **Descrição**: Lista aparelhos com filtros e paginação
- **Ordenação Padrão**: Por nome (crescente)
- **Ordenação Personalizada**: Usar parâmetro `sort=nome,ASC` ou `sort=nome,DESC`
- **Retorno**: `List<AparelhoResponseTO>`

### 6. Listar Aparelhos com Filtro Avançado
- **URL**: `GET /psec/aparelhos/filtragem-ordenacao?filters={json}&page={int}&size={int}`
- **Descrição**: Lista aparelhos com filtro avançado
- **Ordenação**: Por nome (crescente/decrescente conforme filtro)
- **Retorno**: `List<AparelhoResponseTO>`

### 7. Aparelhos Habilitados para Reserva
- **URL**: `GET /psec/aparelhos/habilitados-reserva-equipamento`
- **Descrição**: Lista aparelhos habilitados para reserva de equipamento
- **Ordenação**: Por nome (crescente)
- **Retorno**: `List<AparelhoResponseTO>`

## Campos de Entrada (AparelhoTO)

### Campos Básicos
```json
{
  "id": "Integer (opcional para criação)",
  "nome": "String (obrigatório)",
  "sigla": "String",
  "icone": "String",
  "quantidade": "Integer (default: 0)",
  "ativo": "Boolean (default: true)",
  "crossfit": "Boolean (default: false)",
  "usarEmReservaEquipamentos": "Boolean (default: false)",
  "sensorSelfloops": "String"
}
```

### Campos de Imagem
```json
{
  "imageDataUpload": "String (base64 da imagem - data:image/jpeg;base64,/9j/4AAQ...)",
  "base64Imagem": "String (alias para imageDataUpload)",
  "imageUrl": "String (para remoção: enviar string vazia '')",
  "fotokey": "String (aceito mas não processado)",
  "formControlNomeImagem": "String"
}
```

### Campos Técnicos
```json
{
  "tipoPeso": "String",
  "mecanismo": "String",
  "tipoTrajetoriaMovimento": "String",
  "tipoTrajetoria": "String (alias para tipoTrajetoriaMovimento)",
  "nivelComplexidade": "String",
  "marcaEquipamento": "String",
  "requerInstrutor": "Boolean (default: false)",
  "multifuncao": "Boolean (default: false)",
  "equipamentoAjustavel": "Boolean (default: false)",
  "permiteMovimentosUnilaterais": "Boolean (default: false)",
  "possuiTravaSeguranca": "Boolean (default: false)"
}
```

### Campos de Relacionamento
```json
{
  "ajusteIds": "List<Integer>",
  "atividadeIds": "List<Integer>",
  "novosAjustes": "List<String>",
  "grupoMuscularIds": "List<Integer>",
  "musculoAlvoIds": "List<Integer> (alias para grupoMuscularIds)"
}
```

### Campos IA
```json
{
  "idEquipment_Ia": "Integer",
  "nameEquipment_Ia": "String",
  "isRequired_Ia": "Boolean (default: false)"
}
```

### Campos de Filtro
```json
{
  "aparelhos": "List<String> (['IA', 'CONVENCIONAL'])"
}
```

## Campos de Retorno (AparelhoResponseTO)

### Campos Básicos
```json
{
  "id": "Integer",
  "nome": "String",
  "sigla": "String",
  "icone": "String",
  "quantidade": "Integer",
  "ativo": "Boolean",
  "usarEmReservaEquipamentos": "Boolean",
  "sensorSelfloops": "String"
}
```

### Campos de Imagem
```json
{
  "fotoKey": "String (chave interna da imagem)",
  "imageUrl": "String (URL completa da imagem ou null)",
  "base64Imagem": "String (alias para fotoKey)",
  "imageDataUpload": "String (alias para fotoKey)"
}
```

### Campos Técnicos
```json
{
  "tipoPeso": "String",
  "mecanismo": "String",
  "tipoTrajetoriaMovimento": "String",
  "tipoTrajetoria": "String (alias para tipoTrajetoriaMovimento)",
  "nivelComplexidade": "String",
  "marcaEquipamento": "String",
  "requerInstrutor": "Boolean",
  "multifuncao": "Boolean",
  "equipamentoAjustavel": "Boolean",
  "permiteMovimentosUnilaterais": "Boolean",
  "possuiTravaSeguranca": "Boolean"
}
```

### Campos de Relacionamento
```json
{
  "ajustes": "List<AparelhoAjusteResponseTO>",
  "atividades": "List<AtividadeAparelhoResponseTO>",
  "gruposMusculares": "List<AparelhoGrupoMuscularResponseTO>",
  "musculoAlvoIds": "List<Integer> (IDs dos grupos musculares)"
}
```

## Fluxo de Processamento de Imagens

### 1. Upload de Nova Imagem

**Entrada:**
```json
{
  "nome": "Esteira Profissional",
  "imageDataUpload": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
}
```

**Processamento:**
1. Sistema valida se `imageDataUpload` não está vazio
2. Extrai o base64 da string (remove prefixo `data:image/jpeg;base64,`)
3. Gera identificador único: `{timestamp}-FotoAparelho-{id}`
4. Faz upload para nuvem usando `MidiaService.uploadObjectFromByteArray()`
5. Salva `fotoKey` no banco de dados
6. Adiciona timestamp à `fotoKey`: `{fotoKey}?time={timestamp}`

**Retorno:**
```json
{
  "id": 123,
  "nome": "Esteira Profissional",
  "fotoKey": "12345678-FotoAparelho-123.jpg?time=1234567890",
  "imageUrl": "https://cdn.exemplo.com/fotos/12345678-FotoAparelho-123.jpg?time=1234567890"
}
```

### 2. Remoção de Imagem

**Entrada:**
```json
{
  "nome": "Esteira Profissional",
  "imageUrl": ""
}
```

**Processamento:**
1. Sistema detecta que `imageUrl` é string vazia
2. Define `fotoKey` como `null` no banco de dados
3. Imagem permanece na nuvem mas não é mais referenciada

**Retorno:**
```json
{
  "id": 123,
  "nome": "Esteira Profissional",
  "fotoKey": null,
  "imageUrl": null
}
```

### 3. Consulta sem Alteração de Imagem

**Entrada:**
```json
{
  "nome": "Esteira Profissional Atualizada"
}
```

**Processamento:**
1. Sistema não encontra `imageDataUpload` nem `imageUrl` vazio
2. Mantém `fotoKey` existente no banco
3. Não faz alterações na imagem

**Retorno:**
```json
{
  "id": 123,
  "nome": "Esteira Profissional Atualizada",
  "fotoKey": "12345678-FotoAparelho-123.jpg?time=1234567890",
  "imageUrl": "https://cdn.exemplo.com/fotos/12345678-FotoAparelho-123.jpg?time=1234567890"
}
```

## Construção da URL da Imagem

A URL completa da imagem é construída automaticamente usando:
```java
Aplicacao.obterUrlFotoDaNuvem(fotoKey)
```

**Regras:**
- Se `fotoKey` existe: retorna URL completa da CDN
- Se `fotoKey` é `null`: retorna `null`
- Se `fotoKey` é vazio: retorna URL da foto padrão

## Ordenação dos Resultados

### Ordenação Padrão
Todos os endpoints de listagem de aparelhos ordenam por **nome do aparelho em ordem crescente** por padrão:

- `GET /psec/aparelhos/all` → ORDER BY nome ASC
- `GET /psec/aparelhos` → ORDER BY nome ASC (se não especificado)
- `GET /psec/aparelhos/filtragem-ordenacao` → ORDER BY nome ASC/DESC (conforme filtro)
- `GET /psec/aparelhos/v2` → ORDER BY nome ASC/DESC (conforme filtro)
- `GET /psec/aparelhos/habilitados-reserva-equipamento` → ORDER BY nome ASC

### Ordenação Personalizada
Para endpoints com paginação, use o parâmetro `sort`:

```http
GET /psec/aparelhos?sort=nome,ASC    # Crescente (padrão)
GET /psec/aparelhos?sort=nome,DESC   # Decrescente
```

**Importante**: A ordenação é sempre baseada no **nome do aparelho**, não em outros campos como código ou ID.

## Filtro de Aparelhos IA/Convencionais

### Funcionalidade
O sistema permite filtrar aparelhos entre:
- **Aparelhos da IA**: Criados automaticamente pela inteligência artificial
- **Aparelhos Convencionais**: Criados manualmente pelos usuários

### Lógica de Identificação
- **Aparelho da IA**: `idEquipment_Ia IS NOT NULL`
- **Aparelho Convencional**: `idEquipment_Ia IS NULL`

### Uso do Filtro
```json
{
  "aparelhos": ["IA"]           // Apenas aparelhos da IA
}
```

```json
{
  "aparelhos": ["CONVENCIONAL"] // Apenas aparelhos convencionais
}
```

```json
{
  "aparelhos": ["IA", "CONVENCIONAL"] // Ambos os tipos
}
```

### Endpoints que Suportam o Filtro
- `GET /psec/aparelhos` (filtro básico)
- `GET /psec/aparelhos/filtragem-ordenacao` (filtro avançado)
- `GET /psec/aparelhos/v2` (filtro avançado)

## Compatibilidade e Aliases

### Campos de Entrada Aceitos
- `imageDataUpload` OU `base64Imagem` → mesmo campo
- `grupoMuscularIds` OU `musculoAlvoIds` → mesmo campo  
- `tipoTrajetoriaMovimento` OU `tipoTrajetoria` → mesmo campo
- `fotokey` → aceito mas não processado
- `imageUrl` → usado apenas para remoção (string vazia)

### Campos de Saída Retornados
- `fotoKey` → chave interna
- `imageUrl` → URL completa
- `base64Imagem` → alias para `fotoKey`
- `imageDataUpload` → alias para `fotoKey`
- `tipoTrajetoria` → alias para `tipoTrajetoriaMovimento`
- `musculoAlvoIds` → IDs extraídos de `gruposMusculares`

## Exemplos Práticos

### Exemplo 1: Criar Aparelho com Imagem
```http
POST /psec/aparelhos
Content-Type: application/json

{
  "nome": "Leg Press 45°",
  "sigla": "LP45",
  "quantidade": 2,
  "tipoPeso": "ANILHAS",
  "imageDataUpload": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
  "musculoAlvoIds": [1, 2, 3]
}
```

**Resposta:**
```json
{
  "id": 456,
  "nome": "Leg Press 45°",
  "sigla": "LP45",
  "quantidade": 2,
  "tipoPeso": "ANILHAS",
  "fotoKey": "12345678-FotoAparelho-456.jpg?time=1234567890",
  "imageUrl": "https://cdn.exemplo.com/fotos/12345678-FotoAparelho-456.jpg?time=1234567890",
  "musculoAlvoIds": [1, 2, 3],
  "gruposMusculares": [
    {"grupoMuscularId": 1, "nome": "Quadríceps"},
    {"grupoMuscularId": 2, "nome": "Glúteos"},
    {"grupoMuscularId": 3, "nome": "Panturrilha"}
  ]
}
```

### Exemplo 2: Alterar Aparelho Removendo Imagem
```http
PUT /psec/aparelhos/456
Content-Type: application/json

{
  "nome": "Leg Press 45° - Reformado",
  "imageUrl": ""
}
```

**Resposta:**
```json
{
  "id": 456,
  "nome": "Leg Press 45° - Reformado",
  "fotoKey": null,
  "imageUrl": null
}
```

### Exemplo 3: Consultar Aparelho
```http
GET /psec/aparelhos/456
```

**Resposta:**
```json
{
  "id": 456,
  "nome": "Leg Press 45° - Reformado",
  "sigla": "LP45",
  "quantidade": 2,
  "ativo": true,
  "fotoKey": null,
  "imageUrl": null,
  "tipoPeso": "ANILHAS",
  "musculoAlvoIds": [1, 2, 3],
  "ajustes": [],
  "atividades": [],
  "gruposMusculares": [
    {"grupoMuscularId": 1, "nome": "Quadríceps"},
    {"grupoMuscularId": 2, "nome": "Glúteos"},
    {"grupoMuscularId": 3, "nome": "Panturrilha"}
  ]
}
```

### Exemplo 4: Filtrar Apenas Aparelhos da IA
```http
GET /psec/aparelhos?filters={"aparelhos":["IA"]}&page=0&size=20
```

**Resposta:**
```json
{
  "content": [
    {
      "id": 789,
      "nome": "Leg Press IA",
      "sigla": "LPIA",
      "ativo": true,
      "idEquipment_Ia": 456,
      "nameEquipment_Ia": "AI Leg Press Machine",
      "isRequired_Ia": true,
      "fotoKey": null,
      "imageUrl": null
    }
  ],
  "totalElements": 15,
  "totalPages": 1
}
```

### Exemplo 5: Filtrar Apenas Aparelhos Convencionais
```http
GET /psec/aparelhos?filters={"aparelhos":["CONVENCIONAL"]}&page=0&size=20
```

**Resposta:**
```json
{
  "content": [
    {
      "id": 123,
      "nome": "Esteira Manual",
      "sigla": "EM",
      "ativo": true,
      "idEquipment_Ia": null,
      "nameEquipment_Ia": null,
      "isRequired_Ia": null,
      "fotoKey": "12345678-FotoAparelho-123.jpg?time=1234567890",
      "imageUrl": "https://cdn.exemplo.com/fotos/12345678-FotoAparelho-123.jpg?time=1234567890"
    }
  ],
  "totalElements": 85,
  "totalPages": 5
}
```

## Considerações Importantes

1. **Formato da Imagem**: Aceita base64 com prefixo `data:image/jpeg;base64,` ou similar
2. **Tamanho**: Não há validação de tamanho no backend (deve ser feita no frontend)
3. **Tipos Aceitos**: Qualquer tipo de imagem suportado pelo navegador
4. **Persistência**: Imagens são salvas permanentemente na nuvem
5. **Cache**: URLs incluem timestamp para evitar cache do navegador
6. **Segurança**: Upload requer permissão `RecursoEnum.APARELHOS`

## Troubleshooting

### Erro 400: "Unrecognized field"
- **Causa**: Campo enviado não existe no `AparelhoTO`
- **Solução**: Verificar se o campo está na lista de campos aceitos

### Imagem não aparece
- **Causa**: `fotoKey` é `null` ou URL da CDN está incorreta
- **Solução**: Verificar se upload foi realizado com sucesso

### Upload falha silenciosamente
- **Causa**: Erro no `MidiaService` ou formato de base64 inválido
- **Solução**: Verificar logs do servidor e formato do base64
