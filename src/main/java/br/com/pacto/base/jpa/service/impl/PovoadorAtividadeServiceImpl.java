package br.com.pacto.base.jpa.service.impl;

import br.com.pacto.base.jpa.dto.TreinoAtividadeDTO;
import br.com.pacto.base.jpa.service.intf.PovoadorAtividadeService;
import br.com.pacto.base.util.ExecuteRequestHttpService;
import br.com.pacto.bean.animacao.Animacao;
import br.com.pacto.bean.aparelho.Aparelho;
import br.com.pacto.bean.atividade.Atividade;
import br.com.pacto.bean.atividade.AtividadeAnimacao;
import br.com.pacto.bean.atividade.AtividadeAparelho;
import br.com.pacto.bean.atividade.AtividadeCategoriaAtividade;
import br.com.pacto.bean.atividade.AtividadeGrupoMuscular;
import br.com.pacto.bean.atividade.AtividadeNivel;
import br.com.pacto.bean.atividade.CategoriaAtividade;
import br.com.pacto.bean.atividade.CategoriaAtividadeWodEnum;
import br.com.pacto.bean.atividade.TipoAtividadeEnum;
import br.com.pacto.bean.atividade.UnidadeMedidaEnum;
import br.com.pacto.bean.musculo.GrupoMuscular;
import br.com.pacto.bean.nivel.Nivel;
import br.com.pacto.controller.json.atividade.read.AtividadeJSON;
import br.com.pacto.controller.json.crossfit.AtividadeWodJSON;
import br.com.pacto.controller.json.programa.AtividadeFichaJSON;
import br.com.pacto.dao.intf.atividade.*;
import br.com.pacto.dao.intf.serie.SerieDao;
import br.com.pacto.objeto.Aplicacao;
import br.com.pacto.objeto.JSONMapper;
import br.com.pacto.objeto.Uteis;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.animacao.AnimacaoService;
import br.com.pacto.service.intf.aparelho.AparelhoService;
import br.com.pacto.service.intf.atividade.AtividadeService;
import br.com.pacto.service.intf.atividade.CategoriaAtividadeService;
import br.com.pacto.service.intf.musculo.GrupoMuscularService;
import br.com.pacto.service.intf.nivel.NivelService;
import br.com.pacto.util.UteisValidacao;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.ResultSet;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;

@Service
public class PovoadorAtividadeServiceImpl implements PovoadorAtividadeService {

    @Autowired
    private AtividadeService atividadeService;
    @Autowired
    private CategoriaAtividadeService categoriaAtividadeService;
    @Autowired
    private AtividadeCategoriaAtividadeDao atividadeCategoriaAtividadeDao;
    @Autowired
    private GrupoMuscularService grupoMuscularService;
    @Autowired
    private AtividadeGrupoMuscularDao atividadeGrupoMuscularDao;
    @Autowired
    private NivelService nivelService;
    @Autowired
    private AtividadeNivelDao atividadeNivelDao;
    @Autowired
    private AparelhoService aparelhoService;
    @Autowired
    private AtividadeAparelhoDao atividadeAparelhoDao;
    @Autowired
    private AnimacaoService animacaoService;
    @Autowired
    private AtividadeAnimacaoDao atividadeAnimacaoDao;
    @Autowired
    private AtividadeDao atividadeDao;
    @Autowired
    private AtividadeFichaDao atividadeFichaDao;
    @Autowired
    private SerieDao serieDao;

    @Override
    public String runPovoarByCsv(String ctx, String csvBase64Data) throws Exception {
        /*
         * OBS-01: para este fluxo deve-se obrigatoriamente o arquivo ser do tipo .csv e a divisão das colunas estarem com ; e não ,
         * OBS-02: para que este fluxo atenda a sua necessidade, o arquivo enviado deve estar com as colunas organizadas do seguinte modo:
         * NomeAtividade; Tipo; CategoriaDeAtividade; GrupoMuscular
         */
        try {
            int countSucesso = 0;
            int countError = 0;
            byte[] data = Base64.getDecoder().decode(csvBase64Data);
            try (BufferedReader br = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(data), StandardCharsets.UTF_8))) {
                String line;
                while ((line = br.readLine()) != null) {
                    String[] columns = line.split(";");
                    String nome = columns[0];
                    String tipo = columns[1];
                    String categoriaAtividade = columns[2];
                    String grupoMuscular = columns[3];
                    if (salvarAtividade(ctx, nome, tipo, categoriaAtividade, grupoMuscular, null, null).equals("sucesso")) {
                        countSucesso++;
                    } else {
                        countError++;
                    }
                }
            } catch (Exception ex) {
                Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
                throw new ServiceException(ex);
            }

            System.out.println("\n****************************************");
            System.out.println("PROCESSO CONCLUÍDO COM SUCESSO");
            return countSucesso + " atividades foram salvas com sucesso e " + countError + " atividades não foram salvas por algum problema";
        } catch (Exception ex) {
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
            throw new ServiceException(ex);
        }
    }

    @Override
    public String runPovoarByCsvV2(String ctx, List<TreinoAtividadeDTO> atividades) throws Exception {
        try {
            String retorno = "";
            retorno = "countSucesso atividades foram salvas com sucesso e countError atividades não foram salvas por algum problema";
            retorno += "\n --------------------------------------------";
            int countSucesso = 0;
            int countError = 0;
            for(TreinoAtividadeDTO atividade : atividades) {
                String ret = salvarAtividade(ctx, atividade.getNome(), atividade.getTipoAtividade(),
                        "", "", atividade.getGrupoMuscular(), atividade.getCodigo());
                if (ret.equals("sucesso")) {
                    countSucesso++;
                } else {
                    retorno += "\n"+ret;
                    retorno += "\n --------------------------------------------";
                    countError++;
                }
            }
            System.out.println("\n****************************************");
            System.out.println("PROCESSO CONCLUÍDO COM SUCESSO");
            retorno = retorno.replace("countSucesso", String.valueOf(countSucesso));
            retorno = retorno.replace("countError", String.valueOf(countError));
            return  retorno;
        } catch (Exception ex) {
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
            throw new ServiceException(ex);
        }
    }

    public String importarAtividadeFichaSerieJson(String ctx, List<AtividadeFichaJSON> atividadeFichaJSONS) throws ServiceException {
        try {
            String retorno = "countSucesso AtividadeFicha/Serie foram salvos com sucesso e countErro AtividadeFicha/Serie não foram salvos por algum problema";
            retorno += "\n ------------------------------------------";
            Integer sucesso = 0;
            Integer erro = 0;
            for (AtividadeFichaJSON atividadeFicha : atividadeFichaJSONS) {
                Integer codigoAtividade = atividadeFicha.getCodigoAtividade();
                try {
                    try (ResultSet st = this.atividadeDao.createStatement(ctx, "select codigo from atividade where codigo = " + codigoAtividade)) {
                        if (!st.next()) {
                            try (ResultSet st2 = this.atividadeDao.createStatement(ctx, "select codigo from atividade where nome ilike '" + atividadeFicha.getNomeAtividade()+"' limit 1")) {
                                if (st2.next()) {
                                    codigoAtividade = st2.getInt("codigo");
                                }
                            }
                        }
                    }

                    Integer codigoAtividadeFicha = 0;
                    String sql = String.format("insert into atividadeficha (ficha_codigo, atividade_codigo, ordem, nomeatividadealteradomanualmente) values (%d, %d, %d, false) returning codigo;",
                            atividadeFicha.getCodigoFicha(), codigoAtividade, atividadeFicha.getSequenciaOrdem());
                    try (ResultSet st = this.atividadeFichaDao.createStatement(ctx, sql)) {
                        if (st.next()) {
                            codigoAtividadeFicha = st.getInt("codigo");
                        }
                    }
                    System.out.println("AtividadeFicha: " + codigoAtividadeFicha + " salva com sucesso");

                    if (!UteisValidacao.emptyNumber(codigoAtividadeFicha) && !UteisValidacao.emptyNumber(atividadeFicha.getQtdSeries())) {
                        Integer repeticao = UteisValidacao.somenteNumeros(atividadeFicha.getRepeticoes()) ? Integer.parseInt(atividadeFicha.getRepeticoes()) : 0;
                        String repeticaoComp = !UteisValidacao.somenteNumeros(atividadeFicha.getRepeticoes()) ? atividadeFicha.getRepeticoes() : "";
                        int qtdSeries = atividadeFicha.getQtdSeries();
                        for (int i = 0; i < qtdSeries; i++) {
                            String sql2 = String.format("insert into serie (atividadeficha_codigo, repeticao, repeticaoapp, repeticaocomp, descanso, complemento) values (%d, %d, '%s', '%s', %d, '%s')",
                                    codigoAtividadeFicha, repeticao, repeticaoComp, repeticaoComp, atividadeFicha.getDescancoIntervalo(), atividadeFicha.getObsComplemento());
                            this.serieDao.executeNativeSQL(ctx, sql2);
                            System.out.println("Serie da fichaAtividade: " + codigoAtividadeFicha + " salva com sucesso.");
                        }
                    }
                    sucesso++;
                } catch (Exception ex) {
                    retorno += "\nErro ao inserir atividadeFicha/serie: " + codigoAtividade + " - " + atividadeFicha.getNomeAtividade()+"\n"+ex.getMessage();
                    retorno += "\n ------------------------------------------";
                    erro++;
                    System.out.println("Erro ao inserir atividadeFicha");
                    Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
                }

            }
            retorno = retorno.replace("countSucesso", sucesso.toString());
            retorno = retorno.replace("countErro", erro.toString());
            return retorno;
        } catch (Exception ex) {
            throw new ServiceException(ex);
        }
    }

    private String salvarAtividade(String ctx, String nome, String tipo, String nomeCategoriaAtividade, String nomeGrupoMuscular, Integer codigoGrupoMuscular, Integer codigoAtividade) throws Exception {
        try {
            Atividade atividade = new Atividade();
            if (!UteisValidacao.emptyNumber(codigoAtividade)) {
                atividade.setCodigo(codigoAtividade);
            }
            atividade.setNome(nome);
            atividade.setVersao(0);
            if (UteisValidacao.emptyString(tipo) || tipo.equals(TipoAtividadeEnum.ANAEROBICO.getDescricao())) {
                atividade.setTipo(TipoAtividadeEnum.ANAEROBICO); // Neuromuscular
            } else {
                atividade.setTipo(TipoAtividadeEnum.AEROBICO); // Cardiovascular
            }
            if (!UteisValidacao.emptyNumber(atividade.getCodigo())) {
                atividadeService.apenasValidar(ctx, atividade);
                String sql = String.format("insert into atividade (codigo, nome, ativo, tipo) values (%d, '%s', %s, %d)",
                        atividade.getCodigo(), atividade.getNome(), "true", atividade.getTipo().getId());
                atividadeDao.executeNativeSQL(ctx, sql);
            } else {
                atividadeService.inserir(ctx, atividade);
            }
            salvarAtividadeCategoriaAtividade(ctx, atividade, nomeCategoriaAtividade);
            salvarAtividadeGrupoMuscular(ctx, atividade, nomeGrupoMuscular, codigoGrupoMuscular);
            System.out.println("Atividade salva com sucesso: " + nome);
            return "sucesso";
        } catch (Exception ex) {
            System.out.println("Erro ao salvar a atividade: " + nome);
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
            return "Erro ao salvar a atividade: " + nome + " \n" + ex.getMessage();
        }

    }

    // ****************************************************************************************************************
    // INÍCIO SALVAR ATIVIDADE IMPORTADA DA MATRIZ NA FILIAL
    // ****************************************************************************************************************
    public String salvarAtividadeImportadaDaMatrizNaFilial(String ctxFilial, AtividadeJSON atividadeJSON) throws Exception {
        try {
            Atividade atividade = new Atividade();
            atividade.setNome(atividadeJSON.getNome());
            atividade.setVersao(0);
            atividade.setAtivo(atividadeJSON.isAtivo());
            if (atividadeJSON.getTipo().equals(TipoAtividadeEnum.ANAEROBICO.getId())) {
                atividade.setTipo(TipoAtividadeEnum.ANAEROBICO); // Neuromuscular
            } else {
                atividade.setTipo(TipoAtividadeEnum.AEROBICO); // Cardiovascular
            }
            atividade.setDescricao(atividadeJSON.getDescricao());
            atividade.setLinkVideo(atividadeJSON.getUrlVideo());
            atividadeService.inserir(ctxFilial, atividade);

            for(String nomeCategoriaAtividade : atividadeJSON.getCategorias()) {
                salvarAtividadeCategoriaAtividade(ctxFilial, atividade, nomeCategoriaAtividade);
            }
            for(String nomeGrupoMuscular : atividadeJSON.getGruposMusculares()) {
                salvarAtividadeGrupoMuscular(ctxFilial, atividade, nomeGrupoMuscular, null);
            }
            for(String nomeNivel : atividadeJSON.getNiveis()) {
                salvarAtividadeNivel(ctxFilial, atividade, nomeNivel);
            }
            for(String nomeAparelho : atividadeJSON.getListAparelhos()) {
                salvarAtividadeAparelho(ctxFilial, atividade, nomeAparelho);
            }
            if(!atividadeJSON.getAnimacoes().isEmpty()) {
                for (String nomeAnimacao : atividadeJSON.getAnimacoes()) {
                    salvarAtividadeAnimacao(ctxFilial, atividade, nomeAnimacao, atividadeJSON.getImg(), atividadeJSON.getThumb(), atividadeJSON.getImgMedium());
                }
            } else {
                salvarAtividadeAnimacao(ctxFilial, atividade, null, atividadeJSON.getImg(), atividadeJSON.getThumb(), atividadeJSON.getImgMedium());
            }

            System.out.println("Atividade salva com sucesso: " + atividadeJSON.getNome());
            return "sucesso";
        } catch (Exception ex) {
            System.out.println("Erro ao salvar a atividade: " + atividadeJSON.getNome());
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
            return "erro";
        }
    }

    private void salvarAtividadeGrupoMuscular(String ctx, Atividade atividade, String nomeGrupoMuscular, Integer codigoGrupoMuscular) throws ServiceException {
        try {
            HashMap<String, Object> param = new HashMap<>();
            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM GrupoMuscular obj ");
            hql.append("WHERE 1=1 ");
            if (!UteisValidacao.emptyString(nomeGrupoMuscular)) {
                hql.append("and upper(obj.nome) = :nome ");
                param.put("nome", nomeGrupoMuscular.toUpperCase());
            }
            if(!UteisValidacao.emptyNumber(codigoGrupoMuscular)) {
                hql.append("and obj.codigo = :codigoGrupoMuscular ");
                param.put("codigoGrupoMuscular", codigoGrupoMuscular);
            }
            GrupoMuscular grupoMuscular = grupoMuscularService.obterObjetoPorParam(ctx, hql.toString(), param);

            if (!UteisValidacao.emptyString(nomeGrupoMuscular) &&
                    (grupoMuscular == null || UteisValidacao.emptyNumber(grupoMuscular.getCodigo()))) {
                GrupoMuscular novoGrupoMuscular = new GrupoMuscular(nomeGrupoMuscular);
                grupoMuscularService.inserir(ctx, novoGrupoMuscular);
                grupoMuscular = novoGrupoMuscular;
            }

            if (grupoMuscular != null && !UteisValidacao.emptyString(grupoMuscular.getNome())) {
                AtividadeGrupoMuscular atvGrupoMuscular = new AtividadeGrupoMuscular(grupoMuscular, atividade);
                atividadeGrupoMuscularDao.insert(ctx, atvGrupoMuscular);
            }
        } catch (Exception ex) {
            System.out.println("Erro ao salvar a AtividadeGrupoMuscular grupoMuscular: " + nomeGrupoMuscular + " // atividade: " + atividade.getNome());
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
        }
    }

    private void salvarAtividadeCategoriaAtividade(String ctx, Atividade atividade, String nomeCategoriaAtividade) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM CategoriaAtividade obj WHERE upper(obj.nome) = :nome");
            HashMap<String, Object> param = new HashMap<>();
            param.put("nome", nomeCategoriaAtividade.toUpperCase());
            CategoriaAtividade categoriaAtividade = categoriaAtividadeService.obterObjetoPorParam(ctx, hql.toString(), param);

            if (!UteisValidacao.emptyString(nomeCategoriaAtividade) && (categoriaAtividade == null || UteisValidacao.emptyNumber(categoriaAtividade.getCodigo()))) {
                CategoriaAtividade novaCategoriaAtividade = new CategoriaAtividade(nomeCategoriaAtividade);
                categoriaAtividadeService.inserir(ctx, novaCategoriaAtividade);
                categoriaAtividade = novaCategoriaAtividade;
            }

            if (categoriaAtividade != null && !UteisValidacao.emptyString(categoriaAtividade.getNome())) {
                AtividadeCategoriaAtividade atvCategoriaAtividade = new AtividadeCategoriaAtividade(categoriaAtividade, atividade);
                atividadeCategoriaAtividadeDao.insert(ctx, atvCategoriaAtividade);
            }
        } catch (Exception ex) {
            System.out.println("Erro ao salvar a AtividadeCategoriaAtividade categoriaAtividade: " + nomeCategoriaAtividade + " // atividade: " + atividade.getNome());
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
        }
    }

    private void salvarAtividadeNivel(String ctx, Atividade atividade, String nomeNivel) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM Nivel obj WHERE upper(obj.nome) = :nome");
            HashMap<String, Object> param = new HashMap<>();
            param.put("nome", nomeNivel.toUpperCase());
            Nivel nivel = nivelService.obterObjetoPorParam(ctx, hql.toString(), param);

            if (nivel == null || UteisValidacao.emptyNumber(nivel.getCodigo())) {
                Nivel novoNivel = new Nivel(nomeNivel);
                nivelService.inserir(ctx, novoNivel);
                nivel = novoNivel;
            }

            AtividadeNivel atvNivelAtividade = new AtividadeNivel(nivel, atividade);
            atividadeNivelDao.insert(ctx, atvNivelAtividade);
        } catch (Exception ex) {
            System.out.println("Erro ao salvar a AtividadeNivel nivelAtividade: " + nomeNivel + " // atividade: " + atividade.getNome());
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
        }
    }

    private void salvarAtividadeAparelho(String ctx, Atividade atividade, String nomeAparelho) throws ServiceException {
        try {
            StringBuilder hql = new StringBuilder();
            hql.append("SELECT obj FROM Aparelho obj WHERE upper(obj.nome) = :nome");
            HashMap<String, Object> param = new HashMap<>();
            param.put("nome", nomeAparelho.toUpperCase());
            Aparelho aparelho = aparelhoService.obterObjetoPorParam(ctx, hql.toString(), param);

            if (aparelho == null || UteisValidacao.emptyNumber(aparelho.getCodigo())) {
                Aparelho novoAparelho = new Aparelho(nomeAparelho);
                aparelhoService.inserir(ctx, novoAparelho);
                aparelho = novoAparelho;
            }

            AtividadeAparelho atvAparelhoAtividade = new AtividadeAparelho(aparelho, atividade);
            atividadeAparelhoDao.insert(ctx, atvAparelhoAtividade);
        } catch (Exception ex) {
            System.out.println("Erro ao salvar a AtividadeAparelho aparelhoAtividade: " + nomeAparelho + " // atividade: " + atividade.getNome());
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
        }
    }

    public void salvarAtividadeAnimacao(String ctx, Atividade atividade, String nomeAnimacao, String fotoKey, String fotoKeyPequena, String fotoKeyMin) throws ServiceException {
        try {
            AtividadeAnimacao atvAnimacaoAtividade = new AtividadeAnimacao();
            if(nomeAnimacao != null) {
                StringBuilder hql = new StringBuilder();
                hql.append("SELECT obj FROM Animacao obj WHERE upper(obj.titulo) = :nome");
                HashMap<String, Object> param = new HashMap<>();
                param.put("nome", nomeAnimacao.toUpperCase());
                Animacao animacao = animacaoService.obterObjetoPorParam(ctx, hql.toString(), param);

                if (animacao == null || UteisValidacao.emptyNumber(animacao.getCodigo()) && !UteisValidacao.emptyString(nomeAnimacao)) {
                    animacao = new Animacao(nomeAnimacao);
                    animacaoService.inserir(ctx, animacao);
                }

                if (animacao != null && animacao.getTitulo() != null) {
                    atvAnimacaoAtividade.setAnimacao(animacao);
                    atvAnimacaoAtividade.setAtividade(atividade);
                }
                atividadeAnimacaoDao.insert(ctx, atvAnimacaoAtividade);
            } else if (fotoKey != null && !UteisValidacao.emptyString(fotoKey)) {
                atvAnimacaoAtividade.setFotoKey(fotoKey);
                atvAnimacaoAtividade.setFotoKeyPequena(fotoKeyPequena);
                atvAnimacaoAtividade.setFotoKeyMiniatura(fotoKeyMin);
                atvAnimacaoAtividade.setAtividade(atividade);
                atividadeAnimacaoDao.insert(ctx, atvAnimacaoAtividade);
            }
        } catch (Exception ex) {
            System.out.println("Erro ao salvar a AtividadeAnimacao animacaoAtividade: " + nomeAnimacao + " // atividade: " + atividade.getNome());
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
        }
    }
    // ****************************************************************************************************************
    // FIM SALVAR ATIVIDADE IMPORTADA DA MATRIZ NA FILIAL
    // ****************************************************************************************************************



    // ****************************************************************************************************************
    // INÍCIO IMPORTAR ATIVIDADES CROSS
    // ****************************************************************************************************************
    @Override
    public String runImportarAtividadesCross(final String ctxOrigem, final String ctxDestino) throws Exception {
        /*
         * IMPORTADOR DE ATIVIDADES CROSS:
         * Utiliza uma chave como modelo (ctxOrigem) para importar para a chave desejada (ctxDestino)
         * A importação é realizada para todas unidades caso a ctxDestino tenha mais de uma unidade
         * ctxOrigem utilizada como modelo: f0f6e849216d5398379b17628602a77b - CROSS EXPERIENCE JARDIM ELDORADO - MG
         * KEYSEARCH: importaratividadecross; povoaratividadecross; atividadecross;
         */
        try {
            int countSucesso = 0;
            int countError = 0;
            try {
                List<AtividadeWodJSON> atividadesCrossJSON = obterAtividadesCrossCtxOrigem(ctxOrigem);
                if (UteisValidacao.emptyList(atividadesCrossJSON)) {
                    return ("Não foram localizadas atividades cross na ctxOrigem, processo encerrado");
                }
                for (AtividadeWodJSON atvCrossJSON : atividadesCrossJSON) {
                    if (salvarAtividadeCross(ctxDestino, atvCrossJSON).equals("sucesso")) {
                        countSucesso++;
                    } else {
                        countError++;
                    }
                }
            } catch (Exception ex) {
                Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
                throw new ServiceException(ex);
            }

            return countSucesso + " atividades cross foram importadas com sucesso e " + countError + " atividades não foram salvas por algum problema";
        } catch (Exception ex) {
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
            throw new ServiceException(ex);
        }
    }

    private List<AtividadeWodJSON> obterAtividadesCrossCtxOrigem(final String ctxOrigem) throws Exception {
        JSONObject result = Uteis.getJSON(Aplicacao.getProp(Aplicacao.discoveryUrls) + "/find/" + ctxOrigem);
        String treinoUrl = result.getJSONObject("content").getJSONObject("serviceUrls").getString("treinoUrl");

        HttpPost httpPost = new HttpPost(treinoUrl + "/prest/crossfit/" + ctxOrigem + "/obterAtividadesCrossFit");
        httpPost.setHeader("Content-Type", "application/json");
        HttpClient client = ExecuteRequestHttpService.createConnectorCloseable();
        HttpResponse response = client.execute(httpPost);

        String responseBody = EntityUtils.toString(response.getEntity());
        JSONObject json = new JSONObject(responseBody);
        if (json.has("return")) {
            return JSONMapper.getList(json.getJSONArray("return"), AtividadeWodJSON.class);
        }
        throw new Exception("nao foi possivel obter a lista de atividades");
    }

    private String salvarAtividadeCross(final String ctxDestino, AtividadeWodJSON atvCrossJSON) {
        if (atvCrossJSON == null) {
            return "erro";
        }
        try {
            Atividade atvCross = new Atividade();
            atvCross.setCrossfit(true);
            atvCross.setNome(atvCrossJSON.getNomeAtividade());
            atvCross.setLinkVideo(atvCrossJSON.getLinkVideo());
            atvCross.setDescricao(atvCrossJSON.getDescricaoAtividade());
            atvCross.setUnidadeMedida(UnidadeMedidaEnum.getFromId(atvCrossJSON.getCodUnidadeMedida()));
            atvCross.setCategoriaAtividadeWod(CategoriaAtividadeWodEnum.getFromId(atvCrossJSON.getCodCategoriaAtividadeWod()));
            atvCross.setTodasEmpresas(true);
            atvCross.setAtivo(true);
            atividadeService.inserir(ctxDestino, atvCross);
            return "sucesso";
        } catch (Exception ex) {
            Uteis.logar(ex, PovoadorAtividadeServiceImpl.class);
            return "erro";
        }
    }

    // ****************************************************************************************************************
    // FIM IMPORTAR ATIVIDADES CROSS
    // ****************************************************************************************************************

}
