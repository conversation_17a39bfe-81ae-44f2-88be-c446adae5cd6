package br.com.pacto.controller.json.atividade;

import br.com.pacto.base.dto.EnvelopeRespostaDTO;
import br.com.pacto.base.dto.PaginadorDTO;
import br.com.pacto.base.util.ResponseEntityFactory;
import br.com.pacto.bean.atividade.AtividadeTO;
import br.com.pacto.bean.perfil.permissao.RecursoEnum;
import br.com.pacto.security.aspecto.Permissao;
import br.com.pacto.service.exception.ServiceException;
import br.com.pacto.service.intf.atividade.AtividadeService;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import br.com.pacto.service.impl.atividade.RelatorioExclusaoAtividadeIADTO;

import java.nio.charset.Charset;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;


/**
 * Created by ulisses on 21/08/2018.
 */
@Controller
@RequestMapping("/psec/atividades")
public class AtividadeController {

    private final AtividadeService atividadeService;

    @Autowired
    public AtividadeController(AtividadeService atividadeService){
        Assert.notNull(atividadeService, "O serviço de atividade não foi injetado corretamente");
        this.atividadeService = atividadeService;
    }


    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividades(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                String tipo, PaginadorDTO paginadorDTO, boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            return ResponseEntityFactory.ok(atividadeService.listarAtividades(filtroAtividadeJSON, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/ia", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesIA(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                String tipo, PaginadorDTO paginadorDTO, boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            filtroAtividadeJSON.getTreinoia().add("todas");
            return ResponseEntityFactory.ok(atividadeService.listarAtividades(filtroAtividadeJSON, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/prescricao", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesPrescricao(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                String tipo, PaginadorDTO paginadorDTO, boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            return ResponseEntityFactory.ok(atividadeService.listarAtividades(filtroAtividadeJSON, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/montarTreino/{ficha}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesMontarTreino(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                            @PathVariable Integer ficha,
                                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                String tipo, PaginadorDTO paginadorDTO, boolean crossfit) throws JSONException {
        try {
            ficha = ficha != -1 ? ficha : null;
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros.getJSONObject("filters"));
            paginadorDTO.setSize(0l);
            return ResponseEntityFactory.ok(atividadeService.listarAtividadesMontagemTreino(ficha, filtroAtividadeJSON, false, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/montarTreino", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarAtividadesMontarTreinoSemFicha(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                                            @RequestHeader(value = "empresaId", required = true) Integer empresaId,
                                                                String tipo, PaginadorDTO paginadorDTO, boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeJSON filtroAtividadeJSON = new FiltroAtividadeJSON(filtros);
            paginadorDTO.setSize(100l);
            return ResponseEntityFactory.ok(atividadeService.listarAtividadesMontagemTreino(null, filtroAtividadeJSON, true, tipo, paginadorDTO, crossfit, empresaId), paginadorDTO);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> buscarAtividade(@PathVariable("id") final Integer id) {
        try {
            return ResponseEntityFactory.ok(atividadeService.buscarAtividade(id));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar a atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/replicar/{ctxMatriz}/{codigoEmpresaZWMatriz}/{ctxFilial}/{codigoEmpresaZWFilial}/{status}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarAtividade(@PathVariable("ctxMatriz") String ctxMatriz,
                                                                 @PathVariable("codigoEmpresaZWMatriz") final Integer codigoEmpresaZWMatriz,
                                                                 @PathVariable("ctxFilial") String ctxFilial,
                                                                 @PathVariable("codigoEmpresaZWFilial") final Integer codigoEmpresaZWFilial,
                                                                 @PathVariable("status") Boolean status) {
        try {
            return ResponseEntityFactory.ok(atividadeService.replicarAtividade(ctxMatriz, codigoEmpresaZWMatriz, ctxFilial, codigoEmpresaZWFilial, status));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar replicar as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/config-empresa", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> configEmpresa() {
        try {
            return ResponseEntityFactory.ok(atividadeService.configsEmpresa());
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar buscar a atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> listarTodasAtividades(@RequestParam(value = "filters", required = false) JSONObject filtros, boolean crossfit) throws JSONException {
        try {
            FiltroAtividadeCrossfitJSON filtroAtividadeCrossfitJSON = new FiltroAtividadeCrossfitJSON(filtros);
            /**
             * temporario até o harlei arrumar o front
             */
            filtroAtividadeCrossfitJSON.setCrossfit(crossfit);
            return ResponseEntityFactory.ok(atividadeService.listarTodasAtividades(filtroAtividadeCrossfitJSON));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar listar todas as atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> cadastrarAtividade(@RequestBody AtividadeTO atividadeTO) {
        try {
            return ResponseEntityFactory.ok(atividadeService.cadastrarAtividade(atividadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar cadastrar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividade(@PathVariable("id") final Integer id,
                                                                  @RequestBody AtividadeTO atividadeTO) {
        try {
            atividadeTO.setId(id);
            return ResponseEntityFactory.ok(atividadeService.atualizarAtividade(atividadeTO));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/atualizar-prescricao-ia/{marcar}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarAtividade(@PathVariable("marcar") final String marcar) {
        try {
            atividadeService.toggleAtividadesIA(marcar.equals("t"));
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "situacao/{id}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> atualizarStituacaoAtividade(@PathVariable("id") final Integer id,
                                                                  @RequestBody AtividadeTO atividadeTO) {
        try {
            atividadeTO.setId(id);
            atividadeService.atualizarSituacaoAtividade(atividadeTO, id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar atualizar atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividade(@PathVariable("id") final Integer id){
        try {
            atividadeService.removerAtividade(id);
            return ResponseEntityFactory.ok(true);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar excluir atividade", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/reduzir-tamanho-gif", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> reduzirTamanhoGif(@RequestParam(value = "tamanhoMaximoMB", required = true) double tamanhoMaximoMB,
                                                                 @RequestParam(value = "somenteValidacao", required = true) boolean somenteValidacao,
                                                                 @RequestParam(value = "idAtividade", required = false) Integer idAtividade) {
        try {
            return ResponseEntityFactory.ok(atividadeService.processoReduzirTamanhoGif(tamanhoMaximoMB, somenteValidacao, idAtividade));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao reduzir gif", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/excluir-fotokey-aws-csv", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> excluirFotokeyAwsCsv(@RequestBody Map<String, String> requestBody) {
        try {
            /*
             * Para este fluxo deve-se obrigatoriamente o arquivo ser do tipo .csv e possuir somente a coluna contendo o valor de fotokey
             */
            String csvBase64Data = requestBody.get("fileBase64");
            return ResponseEntityFactory.ok(atividadeService.excluirFotokeyAwsCsv(csvBase64Data));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao remover fotokey da aws s3", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/replicar/imagens/{ctxMatriz}/{codigoEmpresaZWMatriz}/{ctxFilial}/{codigoEmpresaZWFilial}/{substituirImagens}",
            method = RequestMethod.GET,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarImagensAtividades(
            @PathVariable("ctxMatriz") String ctxMatriz,
            @PathVariable("codigoEmpresaZWMatriz") final Integer codigoEmpresaZWMatriz,
            @PathVariable("ctxFilial") String ctxFilial,
            @PathVariable("codigoEmpresaZWFilial") final Integer codigoEmpresaZWFilial,
            @PathVariable("substituirImagens") Boolean substituirImagens) {
        try {
            return ResponseEntityFactory.ok(
                    atividadeService.replicarImagensAtividades(ctxMatriz, codigoEmpresaZWMatriz, ctxFilial, codigoEmpresaZWFilial, substituirImagens)
            );
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao replicar as imagens das atividades", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(
            value = "/desativar-atividades-idia",
            method = RequestMethod.PUT,
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    public ResponseEntity<EnvelopeRespostaDTO> desativarAtividadesComIdia() {
        try {
            return ResponseEntityFactory.ok(atividadeService.desativarAtividadesComIdiaPreenchido());
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName())
                    .log(Level.SEVERE, "Erro ao desativar atividades com idia e idia2 preenchidos", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/remover-geradas-ia", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividadesGeradasPorIA() {
        try {
            RelatorioExclusaoAtividadeIADTO relatorio = atividadeService.removerAtividadesGeradasPorIA(false);
            return ResponseEntityFactory.ok(relatorio);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover atividades geradas por IA", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/remover-geradas-ia/{forcarExclusaoTotal}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> removerAtividadesGeradasPorIAComModo(
            @PathVariable("forcarExclusaoTotal") Boolean forcarExclusaoTotal) {
        try {
            RelatorioExclusaoAtividadeIADTO relatorio = atividadeService.removerAtividadesGeradasPorIA(forcarExclusaoTotal);
            return ResponseEntityFactory.ok(relatorio);
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar remover atividades geradas por IA", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @ResponseBody
    @Permissao(recursos = RecursoEnum.ATIVIDADES)
    @RequestMapping(value = "/replicar-substituindo/{ctxOrigem}/{codigoEmpresaZWOrigem}/{ctxDestino}/{codigoEmpresaZWDestino}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> replicarAtividadesSubstituindo(@PathVariable("ctxOrigem") String ctxOrigem,
                                                                              @PathVariable("codigoEmpresaZWOrigem") Integer codigoEmpresaZWOrigem,
                                                                              @PathVariable("ctxDestino") String ctxDestino,
                                                                              @PathVariable("codigoEmpresaZWDestino") Integer codigoEmpresaZWDestino) {
        try {
            return ResponseEntityFactory.ok(atividadeService.replicarAtividadesSubstituindo(ctxOrigem, codigoEmpresaZWOrigem, ctxDestino, codigoEmpresaZWDestino));
        } catch (ServiceException e) {
            Logger.getLogger(AtividadeController.class.getName()).log(Level.SEVERE, "Erro ao tentar replicar atividades substituindo as existentes", e);
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
