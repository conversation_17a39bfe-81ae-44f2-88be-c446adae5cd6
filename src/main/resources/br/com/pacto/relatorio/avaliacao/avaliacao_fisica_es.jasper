�� sr (net.sf.jasperreports.engine.JasperReport      '� L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      '� +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;L sectionTypet 2Lnet/sf/jasperreports/engine/type/SectionTypeEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  �b            +           J  S        sr +net.sf.jasperreports.engine.base.JRBaseBand      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L returnValuest Ljava/util/List;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      '� L childrenq ~ L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListx����a� I sizexp    w    xp  �b    pppp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xr java.lang.Enum          xpt STRETCH~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xq ~ t LTRpppppsr .net.sf.jasperreports.engine.base.JRBaseSection      '� [ bandst %[Lnet/sf/jasperreports/engine/JRBand;[ partst %[Lnet/sf/jasperreports/engine/JRPart;xpur %[Lnet.sf.jasperreports.engine.JRBand;��~�ʅ5  xp   sq ~ sq ~    /w   /sr 0net.sf.jasperreports.engine.base.JRBaseRectangle      '� L radiust Ljava/lang/Integer;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      '� I PSEUDO_SERIAL_VERSION_UIDL fillq ~ L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;xr .net.sf.jasperreports.engine.base.JRBaseElement      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ L 	forecolorq ~ 2L keyq ~ L modeq ~ L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangest %Lnet/sf/jasperreports/engine/JRGroup;L 
propertiesMapq ~ [ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;L uuidt Ljava/util/UUID;xp  �b   )       &        sr java.awt.Color���3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ =xp    ����pppq ~ q ~ *pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCHsr java.util.UUID����m�/ J leastSigBitsJ mostSigBitsxp��/v��=CՈ�O�  w�ppsr *net.sf.jasperreports.engine.base.JRBasePen      '� I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 2L 	lineStyleq ~ L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidtht Ljava/lang/Float;L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  �bsq ~ ;    ����pppppsr java.lang.Float��ɢ�<�� F valuexr java.lang.Number������  xp    q ~ :psr 1net.sf.jasperreports.engine.base.JRBaseStaticText      '� L textq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      '� I PSEUDO_SERIAL_VERSION_UIDL fontNameq ~ L fontSizeq ~ -L fontsizeq ~ IL horizontalAlignmentq ~ L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L horizontalTextAlignt :Lnet/sf/jasperreports/engine/type/HorizontalTextAlignEnum;L isBoldt Ljava/lang/Boolean;L isItalicq ~ TL 
isPdfEmbeddedq ~ TL isStrikeThroughq ~ TL isStyledTextq ~ TL isUnderlineq ~ TL lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~ L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L 	paragrapht )Lnet/sf/jasperreports/engine/JRParagraph;L pdfEncodingq ~ L pdfFontNameq ~ L rotationq ~ L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L verticalAlignmentq ~ L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;L verticalTextAlignt 8Lnet/sf/jasperreports/engine/type/VerticalTextAlignEnum;xq ~ 1  �b   )       &        pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�d����+��y�M�A�  �bppsq ~ MA`  pp~r 8net.sf.jasperreports.engine.type.HorizontalTextAlignEnum          xq ~ t CENTERsr java.lang.Boolean� r�՜�� Z valuexppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      '� L 
bottomPaddingq ~ -L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ -L leftPenq ~ dL paddingq ~ -L penq ~ dL rightPaddingq ~ -L rightPenq ~ dL 
topPaddingq ~ -L topPenq ~ dxppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      '�  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      '� L lineBoxq ~ Uxq ~ G  �bsq ~ ;    �   pppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsq ~ M    q ~ fq ~ fq ~ [sr java.lang.Integer⠤���8 I valuexq ~ N    sr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      '�  xq ~ h  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~ fq ~ fpsq ~ h  �bppppq ~ fq ~ fpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      '�  xq ~ h  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~ fq ~ fpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      '�  xq ~ h  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~ fq ~ fpppsr 0net.sf.jasperreports.engine.base.JRBaseParagraph      '� 
L firstLineIndentq ~ -L 
leftIndentq ~ -L lineSpacingq ~ VL lineSpacingSizeq ~ IL paragraphContainert 2Lnet/sf/jasperreports/engine/JRParagraphContainer;L rightIndentq ~ -L spacingAfterq ~ -L 
spacingBeforeq ~ -L tabStopWidthq ~ -L tabStopsq ~ xpppppq ~ [ppppppppppp~r 6net.sf.jasperreports.engine.type.VerticalTextAlignEnum          xq ~ t MIDDLEt Evaluación físicasr ,net.sf.jasperreports.engine.base.JRBaseImage      '� I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isLazyB onErrorTypeL anchorNameExpressionq ~ L evaluationGroupq ~ 6L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L horizontalAlignmentq ~ L horizontalAlignmentValueq ~ RL horizontalImageAlignt ;Lnet/sf/jasperreports/engine/type/HorizontalImageAlignEnum;L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L hyperlinkWhenExpressionq ~ L isUsingCacheq ~ TL lineBoxq ~ UL 
linkTargetq ~ L linkTypeq ~ L onErrorTypeValuet 2Lnet/sf/jasperreports/engine/type/OnErrorTypeEnum;L 
scaleImageq ~ L scaleImageValuet 1Lnet/sf/jasperreports/engine/type/ScaleImageEnum;L verticalAlignmentq ~ L verticalAlignmentValueq ~ YL verticalImageAlignt 9Lnet/sf/jasperreports/engine/type/VerticalImageAlignEnum;xq ~ .  �b   K        Z   	   9pq ~ q ~ *pppppp~q ~ ?t FIX_RELATIVE_TO_BOTTOMpppp~q ~ Bt RELATIVE_TO_TALLEST_OBJECTsq ~ E��[�)L�_��Rk �GU  w�ppsq ~ G  �bppppq ~ �  �b         pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t REPORTsr 1net.sf.jasperreports.engine.base.JRBaseExpression      '� I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L typet 5Lnet/sf/jasperreports/engine/type/ExpressionTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mY��iK�U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      '� B typeL textq ~ xpt 	fotoAlunoppppp~r 9net.sf.jasperreports.engine.type.HorizontalImageAlignEnum          xq ~ t CENTERppppppsq ~ a sq ~ cpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ q  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ v  �bppppq ~ �q ~ �psq ~ z  �bppppq ~ �q ~ �pp~r 0net.sf.jasperreports.engine.type.OnErrorTypeEnum          xq ~ t ICONp~r /net.sf.jasperreports.engine.type.ScaleImageEnum          xq ~ t RETAIN_SHAPEpppsr 0net.sf.jasperreports.engine.base.JRBaseTextField      '� I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupq ~ 6L evaluationTimeValueq ~ �L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParametersq ~ �L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L hyperlinkWhenExpressionq ~ L isBlankWhenNullq ~ TL 
linkTargetq ~ L linkTypeq ~ L patternq ~ L patternExpressionq ~ xq ~ Q  �b          �   p   8pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�	A�R�q���`%�@S  �bt Arialpsq ~ MA`  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ q  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ v  �bppppq ~ �q ~ �psq ~ z  �bppppq ~ �q ~ �pppsq ~ ~ppppq ~ �pppppppppppp  �b        pp~q ~ �t NOWsq ~ �   uq ~ �   sq ~ �t 	nomeAlunoppppppppppppppsq ~ �  �b           �   �   Qpq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E��	�����IsHc  �bt Arialpsq ~ MA@  pppppppppsq ~ cpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ q  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ v  �bppppq ~ �q ~ �psq ~ z  �bppppq ~ �q ~ �pppsq ~ ~ppppq ~ �pppppppppppp  �b        pp~q ~ �t PAGEsq ~ �   uq ~ �   sq ~ �t idadeppppppppppppppsq ~ P  �b           D   p   Qpq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�Uf�k#�$�2�u�BS  �bt Arialpsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ q  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ v  �bppppq ~ �q ~ �psq ~ z  �bppppq ~ �q ~ �pppsq ~ ~ppppq ~ �ppppppppppppt Edad:sq ~ �  �b          p   �   `pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�T�*1\2Ҩ.m�9�Al  �bt Arialpsq ~ MA@  pppppppppsq ~ cpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ q  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ v  �bppppq ~ �q ~ �psq ~ z  �bppppq ~ �q ~ �pppsq ~ ~ppppq ~ �pppppppppppp  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t 	avaliadorppppppppppppppsq ~ P  �b           D   p   `pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E���AT�'�e�B�  �bt Arialpsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ q  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ v  �bppppq ~ �q ~ �psq ~ z  �bppppq ~ �q ~ �pppsq ~ ~ppppq ~ �ppppppppppppt 
Evaluador:sq ~ P  �b           D  T   Qpq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�4�K�B��^{��N
  �bt Arialpsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~ �q ~ �q ~ �psq ~ q  �bppppq ~ �q ~ �psq ~ h  �bppppq ~ �q ~ �psq ~ v  �bppppq ~ �q ~ �psq ~ z  �bppppq ~ �q ~ �pppsq ~ ~ppppq ~ �ppppppppppppt Sexo:sq ~ �  �b           �  �   Qpq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E����lxE
;DF��B)  �bt Arialpsq ~ MA@  pppppppppsq ~ cpsq ~ g  �bppppq ~	q ~	q ~psq ~ q  �bppppq ~	q ~	psq ~ h  �bppppq ~	q ~	psq ~ v  �bppppq ~	q ~	psq ~ z  �bppppq ~	q ~	pppsq ~ ~ppppq ~pppppppppppp  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t sexoppppppppppppppsq ~ P  �b           D   p   opq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�C���>�CU�����J�  �bt Arialpsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~ppppppppppppt Fecha:sq ~ �  �b           �   �   opq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�W�83�l>��&MGh  �bt Arialpsq ~ MA@  pppppppppsq ~ cpsq ~ g  �bppppq ~$q ~$q ~ psq ~ q  �bppppq ~$q ~$psq ~ h  �bppppq ~$q ~$psq ~ v  �bppppq ~$q ~$psq ~ z  �bppppq ~$q ~$pppsq ~ ~ppppq ~ pppppppppppp  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t 
dataAvaliacaoppppppppppppppsq ~ P  �b           D  T   opq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�gw���8�-0W��O�  �bt Arialpsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~3q ~3q ~/psq ~ q  �bppppq ~3q ~3psq ~ h  �bppppq ~3q ~3psq ~ v  �bppppq ~3q ~3psq ~ z  �bppppq ~3q ~3pppsq ~ ~ppppq ~/ppppppppppppt 	Próximo:sq ~ �  �b           �  �   opq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E����`�E�2,'|�KK  �bt Arialpsq ~ MA@  pppppppppsq ~ cpsq ~ g  �bppppq ~?q ~?q ~;psq ~ q  �bppppq ~?q ~?psq ~ h  �bppppq ~?q ~?psq ~ v  �bppppq ~?q ~?psq ~ z  �bppppq ~?q ~?pppsq ~ ~ppppq ~;pppppppppppp  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t proximaAvaliacaoppppppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      '� I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xq ~ .  �b          &       �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�g��{��r����MF�  w�ppsq ~ G  �bppppq ~L  �b ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNsq ~ P  �b           �       �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�h�g�^E4m^�Hv  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~Vq ~Vq ~Rpsq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Vq ~Vpsq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~Vq ~Vpsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Vq ~Vpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Vq ~Vpppsq ~ ~ppppq ~Rpppppppppppq ~ �t Indice de Masa Corporal (IMC)sq ~ P  �b           �  �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�	B焿�F�:QyB�  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~lq ~lq ~hpsq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~lq ~lpsq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~lq ~lpsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~lq ~lpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~lq ~lpppsq ~ ~ppppq ~hpppppppppppq ~ �t Peso actualsq ~ P  �b           �     �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�Z7�9g�Ӫ\H1��OR  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~~psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~~pppppppppppq ~ �t Riesgo Cardiovascularsq ~J  �b   /           �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�N�<��`Bp]��ME  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~J  �b   /             �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E����f��'�G�GR�I�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~J  �b   /          �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E��s&7��4�AL  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~ P  �b           �   �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�,?�S���J	�Iz  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Composición Corporalsq ~ �  �b   -        �       �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�?'#��M��+��	J  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t imcppppppppppppppsq ~ �  �b   -        �     �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�^�'L|��ÞM��D7  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t circunferenciappppppppppppppsq ~ �  �b   -        �  �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E��qQ�f/>X�mH6  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t pesoppppppppppppppsq ~ P  �b           �     �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�V�¬��B#���@J  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Circunferencia abdominalsq ~ P  �b           �       �pq ~ q ~ *sq ~ ;    �   ppppppppq ~ @ppppq ~ Csq ~ E���|��S�q],@Lq  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bpppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t IMCsq ~ �  �b   '        �    pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E����) �T��+��CO  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~ppppppppppp~q ~ �t TOP  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t circunferenciaResultadoppppppppppppppsq ~ P  �b           �    pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�|��o AoqӼB  �bppsq ~ M@�  ppq ~ _ppppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bsq ~ ;    ����pppppsq ~ M?�  q ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �t 
Resultado:sq ~ P  �b           �   �  pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E���F�@��g.@b  �bppsq ~ M@�  ppq ~ _ppppppsq ~ cpsq ~ g  �bppppq ~#q ~#q ~ psq ~ q  �bppppq ~#q ~#psq ~ h  �bppppq ~#q ~#psq ~ v  �bppppq ~#q ~#psq ~ z  �bsq ~ ;    ����pppppsq ~ M?�  q ~#q ~#pppsq ~ ~ppppq ~ pppppppppppq ~ �t 
Resultado:sq ~ P  �b           �      pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E���p������, EN  �bppsq ~ M@�  ppq ~ _ppppppsq ~ cpsq ~ g  �bppppq ~0q ~0q ~-psq ~ q  �bppppq ~0q ~0psq ~ h  �bppppq ~0q ~0psq ~ v  �bppppq ~0q ~0psq ~ z  �bppppq ~0q ~0pppsq ~ ~ppppq ~-pppppppppppq ~ �t 
Resultado:sq ~ �  �b           �   �  pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�W�R�
��MF�E  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~<q ~<q ~8psq ~ q  �bppppq ~<q ~<psq ~ h  �bppppq ~<q ~<psq ~ v  �bppppq ~<q ~<psq ~ z  �bppppq ~<q ~<pppsq ~ ~ppppq ~8pppppppppppq ~
  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t composicaoResultadoppppppppppppppsq ~ �  �b           �      'pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E��������݄C+��IE  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~Kq ~Kq ~Gpsq ~ q  �bppppq ~Kq ~Kpsq ~ h  �bppppq ~Kq ~Kpsq ~ v  �bppppq ~Kq ~Kpsq ~ z  �bppppq ~Kq ~Kpppsq ~ ~ppppq ~Gpppppppppppq ~
  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t imcResultadoppppppppppppppsq ~ �  �b           A       �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E��P>�'Y����L�  �bt Arialpsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bppppq ~Zq ~Zq ~Vpsq ~ q  �bppppq ~Zq ~Zpsq ~ h  �bppppq ~Zq ~Zpsq ~ v  �bsq ~ ;    ����pppppsq ~ M?�  q ~Zq ~Zpsq ~ z  �bppppq ~Zq ~Zpppsq ~ ~ppppq ~Vpppppppppppq ~ �  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t alturappppppppppppppsq ~ �  �b           A   A   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�9J����3Wm}O.  �bt Arialpsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bppppq ~kq ~kq ~gpsq ~ q  �bppppq ~kq ~kpsq ~ h  �bppppq ~kq ~kpsq ~ v  �bppppq ~kq ~kpsq ~ z  �bppppq ~kq ~kpppsq ~ ~ppppq ~gpppppppppppq ~ �  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t pesoppppppppppppppsq ~ P  �b   	        A      pq ~ q ~ *sq ~ ;    �   ppppppppq ~ @ppppq ~ Csq ~ E�H��G��~(���D�  �bppsq ~ M@�  ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~zq ~zq ~vpsq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~zq ~zpsq ~ h  �bpppsq ~ M?�  q ~zq ~zpsq ~ v  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~zq ~zpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~zq ~zpppsq ~ ~ppppq ~vpppppppppppq ~ �t Alturasq ~ P  �b   	        A   A  pq ~ q ~ *sq ~ ;    �   ppppppppq ~ @ppppq ~ Csq ~ E��Q�˘z��?�UC&  �bppsq ~ M@�  ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bpppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Pesosq ~ �  �b           s   �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�f�l��_�&FO  �bt Arialpsq ~ MA  pp~q ~ ^t LEFTq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bsq ~ ;    ���pppppsq ~ M@�  q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t "  Grasa: " + sq ~ �t percGordurappppppppppppppsq ~ �  �b           s   �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E���O�],웕20nJ2  �bt Arialpsq ~ MA  ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bsq ~ ;    � Z�pppppsq ~ M@�  q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t "  Residuo: " + sq ~ �t percResiduosppppppppppppppsq ~ �  �b           s   �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E���<�2�/�iJ  �bt Arialpsq ~ MA  ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bsq ~ ;    � �pppppsq ~ M@�  q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �    uq ~ �   sq ~ �t "  Huesos: " + sq ~ �t 	percOssosppppppppppppppsq ~ �  �b           s   �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E��˸L�PVyQC^G5  �bt Arialpsq ~ MA  ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bsq ~ ;    ��  pppppsq ~ M@�  q ~�q ~�psq ~ h  �bpppsq ~ M    q ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   !uq ~ �   sq ~ �t "  Musculos: " + sq ~ �t percMusculosppppppppppppppsq ~ �  �b           v  �   �pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�ص:�'\�U�[Ck  �bt Arialpsq ~ MA   ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   "uq ~ �   sq ~ �t peso1ppppppppppppppsq ~ �  �b           v  �  pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�'@y؝ysOE�4J�  �bt Arialpsq ~ MA   ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~�psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   #uq ~ �   sq ~ �t peso2ppppppppppppppsq ~ �  �b           v  �  +pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�����浨���I�H4  �bt Arialpsq ~ MA   ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~
psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~
pppppppppppq ~ �  �b        ppq ~ �sq ~ �   $uq ~ �   sq ~ �t peso4ppppppppppppppsq ~ �  �b           v  �  pq ~ q ~ *ppppppq ~ @ppppq ~ Csq ~ E�(1�m��-bQ�+F   �bt Arialpsq ~ MA   ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~ q ~ q ~psq ~ q  �bppppq ~ q ~ psq ~ h  �bppppq ~ q ~ psq ~ v  �bppppq ~ q ~ psq ~ z  �bppppq ~ q ~ pppsq ~ ~ppppq ~pppppppppppq ~ �  �b        ppq ~ �sq ~ �   %uq ~ �   sq ~ �t peso3ppppppppppppppsq ~ �  �b           �   �  Hpq ~ q ~ *ppppppq ~ @sq ~ �   &uq ~ �   sq ~ �t showMetappppppq ~ Csq ~ E�����r p|LA�  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~3q ~3q ~+psq ~ q  �bppppq ~3q ~3psq ~ h  �bppppq ~3q ~3psq ~ v  �bppppq ~3q ~3psq ~ z  �bppppq ~3q ~3pppsq ~ ~ppppq ~+pppppppppppq ~
  �b        ppq ~ �sq ~ �   'uq ~ �   sq ~ �t metaGordurappppppppppppppsq ~ P  �b           �   �  6pq ~ q ~ *ppppppq ~ @sq ~ �   (uq ~ �   sq ~ �t showMetappppppq ~ Csq ~ E��_���ɟ�5&���CR  �bppsq ~ M@�  ppq ~ _ppppppsq ~ cpsq ~ g  �bppppq ~Eq ~Eq ~>psq ~ q  �bppppq ~Eq ~Epsq ~ h  �bppppq ~Eq ~Epsq ~ v  �bppppq ~Eq ~Epsq ~ z  �bsq ~ ;    ����pppppsq ~ M?�  q ~Eq ~Epppsq ~ ~ppppq ~>pppppppppppq ~ �t -Meta de % de gordura da próxima avaliação:xp  �b  tpppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~Oppppppq ~ @ppppq ~ Csq ~ E� 喆C�v3f���K�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~Qpsq ~ P  �b   )       &       pq ~ q ~Oppppppq ~ @ppppq ~ Csq ~ E�ݜ��\�]�S�eF�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~Zq ~Zq ~Wq ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Zq ~Zpsq ~ h  �bppppq ~Zq ~Zpsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Zq ~Zpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Zq ~Zpppsq ~ ~ppppq ~Wpppppppppppq ~ �t Objetivos del estudiantesq ~ �  �b   '       &       6pq ~ q ~Oppppppq ~ @ppppq ~ Csq ~ E�c�g�/� c�g�eK�  �bt Arialpsq ~ MA   ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~nq ~nq ~jpsq ~ q  �bppppq ~nq ~npsq ~ h  �bppppq ~nq ~npsq ~ v  �bppppq ~nq ~npsq ~ z  �bppppq ~nq ~npppsq ~ ~ppppq ~jpppppppppppq ~
  �b        ppq ~ �sq ~ �   *uq ~ �   sq ~ �t objetivosAlunoppppppppppppppxp  �b   csq ~ �   )uq ~ �   sq ~ �t 
showobjetivospppppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~}ppppppq ~ @ppppq ~ Csq ~ E����0j%�_ޚ�C�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~psq ~ P  �b   )       &       pq ~ q ~}ppppppq ~ @ppppq ~ Csq ~ E�7��0���|O�� A�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Recomendaciones del Evaluadorsq ~ ,  �b   �       &      5pq ~ q ~}ppppppq ~ @ppppq ~ �sq ~ E�D��gK�β=Dx  w�ppsq ~ G  �bppppq ~�sq ~ o   
sq ~ �  �b   �          
   <pq ~ q ~}ppppppq ~ @ppppq ~ Csq ~ E�_�]nܼ�rT%[��B�  �bt Arialpsq ~ MA   ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~
  �b       ppq ~ �sq ~ �   ,uq ~ �   sq ~ �t 
recomendacoesppppppppppppppxp  �b  sq ~ �   +uq ~ �   sq ~ �t showrecomendacoesppppppq ~ sq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E�=V��.O��P�JI�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~�psq ~ P  �b   )       &       pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E��̐����O �wN�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 	Anamnesissr ,net.sf.jasperreports.engine.base.JRBaseBreak      '� I PSEUDO_SERIAL_VERSION_UIDB typeL 	typeValuet 0Lnet/sf/jasperreports/engine/type/BreakTypeEnum;xq ~ 1  �b           &       pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E�d}��ȝf�*�-5JOV  �b ~r .net.sf.jasperreports.engine.type.BreakTypeEnum          xq ~ t PAGEsr 0net.sf.jasperreports.engine.base.JRBaseSubreport      '� 	L connectionExpressionq ~ L dataSourceExpressionq ~ L 
expressionq ~ L isUsingCacheq ~ TL overflowTypet /Lnet/sf/jasperreports/engine/type/OverflowType;[ 
parameterst 3[Lnet/sf/jasperreports/engine/JRSubreportParameter;L parametersMapExpressionq ~ [ returnValuest 5[Lnet/sf/jasperreports/engine/JRSubreportReturnValue;L runToBottomq ~ Txq ~ 1  �b   [       &       1pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E�D�D[���V�@�5N�psq ~ �   .uq ~ �   sq ~ �t 
anamneseJRpppsq ~ �   /uq ~ �   sq ~ �t 
SUBREPORT_DIRsq ~ �t  + "anamnese.jasper"pppppppppxp  �b   �sq ~ �   -uq ~ �   sq ~ �t showanamneseppppppq ~ sq ~ sq ~    w   sq ~ ,  �b   )       &       
sq ~ ;    ����pppq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E�>����է��R�*@�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~�psq ~ P  �b   )       &       
pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E���\h���]�tDLd  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Par-Qsq ~�  �b           %       pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E�� 9.��p��@i  �b q ~�sq ~�  �b          &      ;pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E�ӟ�S�T�Qd@%�Fspsq ~ �   1uq ~ �   sq ~ �t parqJRpppsq ~ �   2uq ~ �   sq ~ �t 
SUBREPORT_DIRsq ~ �t  + "anamnese.jasper"pppppppppsq ~ �  �b   -       4   ~   zpq ~ q ~�pppppp~q ~ ?t FLOATppppq ~ Csq ~ E�ݺ�@�޵��"�D�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~q ~psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �  �b        ppq ~ �sq ~ �   3uq ~ �   sq ~ �t parqppppppppppppppsq ~ P  �b          4   ~   `pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���
����Ly�ErA�  �bppsq ~ MA`  ppq ~ _q ~ �pppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~)q ~)q ~&q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~)q ~)psq ~ h  �bppppq ~)q ~)psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~)q ~)psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~)q ~)pppsq ~ ~ppppq ~&pppppppppppq ~ �t 
Resultado:sq ~ �  �b          )       �pq ~ q ~�ppppppq ~sq ~ �   4uq ~ �   sq ~ �t apresentarAssinaturappppppq ~ Csq ~ E��xw�k9{�Ę?x�Mi  w�ppsq ~ G  �bppppq ~9  �b         ppq ~ �sq ~ �   5uq ~ �   sq ~ �t 
assinaturapppppq ~ �ppppppq ~ �sq ~ cpsq ~ g  �bppppq ~Dq ~Dq ~9psq ~ q  �bppppq ~Dq ~Dpsq ~ h  �bppppq ~Dq ~Dpsq ~ v  �bppppq ~Dq ~Dpsq ~ z  �bppppq ~Dq ~Dppq ~ �pq ~ �pp~r 7net.sf.jasperreports.engine.type.VerticalImageAlignEnum          xq ~ t MIDDLEsq ~ P  �b          �   P  0pq ~ q ~�ppppppq ~sq ~ �   6uq ~ �   sq ~ �t apresentarAssinaturappppppq ~ Csq ~ E���a�g���oܱpH�  �bppsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Tq ~Tq ~Mq ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Tq ~Tpsq ~ h  �bppppq ~Tq ~Tpsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Tq ~Tpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Tq ~Tpppsq ~ ~ppppq ~Mpppppppppppq ~ �t Firma del Evaluadorxp  �b  Lsq ~ �   0uq ~ �   sq ~ �t showparqpppppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E��9�Q�;3���E�DM  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~jpsq ~ P  �b   )       &       pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E��ɐ�(�r~sD�G�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~sq ~sq ~pq ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~sq ~spsq ~ h  �bppppq ~sq ~spsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~sq ~spsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~sq ~spppsq ~ ~ppppq ~ppppppppppppq ~ �t 6Peso, altura, presión arterial y frecuencia cardíacasq ~�  �b           "       pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E�+w`7��5M��Jv  �b q ~�sq ~J  �b   Z           �   ;pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E�� �	Qr���UYA0  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~J  �b   Z             ;pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E�
���v�m6�o�Ev  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~J  �b   Z          �   ;pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E��KM	�D+ƺqjM�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~ P  �b           �      ;pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E��|�G�P�
�Mf  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Peso actualsq ~ P  �b           �   �   ;pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E�B���l3.3bSTA]  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Alturasq ~ P  �b           �     ;pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E�Q.�L[)�;1[K�  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Pressão Arterialsq ~ P  �b           �  �   ;pq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E���iz�8�����B  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Frec. cardiaca en repososq ~ �  �b   -        �      Ppq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E�R��Nd ��e3�J.  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   7uq ~ �   sq ~ �t 	pesoAtualppppppppppppppsq ~ P  �b           �      }pq ~ q ~hsq ~ ;    �   ppppppppq ~ @ppppq ~ Csq ~ E�^��C�)�����	I
  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bpppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t kilossq ~ �  �b   -        �   �   Ppq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E����xC��w�}Z��K  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �  �b        ppq ~ �sq ~ �   8uq ~ �   sq ~ �t alturaAtualppppppppppppppsq ~ P  �b           �   �   }pq ~ q ~hsq ~ ;    �   ppppppppq ~ @ppppq ~ Csq ~ E���5��_&�9�\N5  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~q ~q ~psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~psq ~ h  �bpppsq ~ M?�  q ~q ~psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �t metrossq ~ P  �b           �     }pq ~ q ~hsq ~ ;    �   ppppppppq ~ @ppppq ~ Csq ~ E�����ն#BUY�r9Oz  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~3q ~3q ~/psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~3q ~3psq ~ h  �bpppsq ~ M?�  q ~3q ~3psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~3q ~3psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~3q ~3pppsq ~ ~ppppq ~/pppppppppppq ~ �t mmHgsq ~ �  �b   -        �     Ppq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E���+��N���V�M  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~Gq ~Gq ~Dpsq ~ q  �bppppq ~Gq ~Gpsq ~ h  �bppppq ~Gq ~Gpsq ~ v  �bppppq ~Gq ~Gpsq ~ z  �bppppq ~Gq ~Gpppsq ~ ~ppppq ~Dpppppppppppq ~ �  �b        ppq ~ �sq ~ �   9uq ~ �   sq ~ �t pressaoArterialppppppppppppppsq ~ �  �b   -        �  �   Ppq ~ q ~hppppppq ~ @ppppq ~ Csq ~ E�r��B��e��S��N�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~Uq ~Uq ~Rpsq ~ q  �bppppq ~Uq ~Upsq ~ h  �bppppq ~Uq ~Upsq ~ v  �bppppq ~Uq ~Upsq ~ z  �bppppq ~Uq ~Upppsq ~ ~ppppq ~Rpppppppppppq ~ �  �b        ppq ~ �sq ~ �   :uq ~ �   sq ~ �t freqCardiacappppppppppppppsq ~ P  �b           �  �   }pq ~ q ~hsq ~ ;    �   ppppppppq ~ @ppppq ~ Csq ~ E�;����7�O�ʁD�  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~dq ~dq ~`psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~dq ~dpsq ~ h  �bpppsq ~ M?�  q ~dq ~dpsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~dq ~dpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~dq ~dpppsq ~ ~ppppq ~`pppppppppppq ~ �t bpmxp  �b   �pppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~uppppppq ~ @ppppq ~ Csq ~ E��fD�p����FjJE  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~wpsq ~ P  �b   )       &       pq ~ q ~uppppppq ~ @ppppq ~ Csq ~ E�	n����p��{M�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~}q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~}pppppppppppq ~ �t Pliegues de la pielsq ~ �  �b          &       8pq ~ q ~uppppppq ~ @ppppq ~ Csq ~ E��3�`��j?�7\J
  �bppsq ~ MA@  ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~
  �b        ppq ~ �sq ~ �   <uq ~ �   sq ~ �t " Protocolo: " + sq ~ �t 	protocoloppppppppppppppsq ~�  �b          &       Lpq ~ q ~uppppppq ~ppppq ~ Csq ~ E�;������/ͯ�_D#psq ~ �   =uq ~ �   sq ~ �t dobrasJRpppsq ~ �   >uq ~ �   sq ~ �t 
SUBREPORT_DIRsq ~ �t  + "dobras.jasper"pppppppppxp  �b   csq ~ �   ;uq ~ �   sq ~ �t 
showdobraspppppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�Ȱ`(
;�5�9o}@�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~�psq ~ P  �b   )       &       pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���Ȃ+�.ch=K�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Perimetría y diámetrossq ~�  �b   B             8pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���kXs��2��A�Nvpsq ~ �   @uq ~ �   sq ~ �t perimetriaJRpppsq ~ �   Auq ~ �   sq ~ �t 
SUBREPORT_DIRsq ~ �t  + "perimetria.jasper"pppppppppsq ~ �  �b           [  �   Qpq ~ q ~�ppppppq ~sq ~ �   Buq ~ �   sq ~ �t !sq ~ �t 
diametroPunhosq ~ �t .equals("")ppppppq ~ Csq ~ E�M@s�I< %Jخ>I4  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppp  �b       ppq ~ �sq ~ �   Cuq ~ �   sq ~ �t 
diametroPunhoppppppppppppppsq ~ P  �b           �     Qpq ~ q ~�ppppppq ~sq ~ �   Duq ~ �   sq ~ �t !sq ~ �t 
diametroPunhosq ~ �t .equals("")ppppppq ~ Csq ~ E���E�z[V2}�I  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�ppppppppppppt Diámetro de la muñecasq ~ �  �b           [  �   gpq ~ q ~�ppppppq ~sq ~ �   Euq ~ �   sq ~ �t !sq ~ �t diametroJoelhosq ~ �t .equals("")ppppppq ~ Csq ~ E��I(�d�p��!`K�  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppp  �b       ppq ~ �sq ~ �   Fuq ~ �   sq ~ �t diametroJoelhoppppppppppppppsq ~ P  �b           �     gpq ~ q ~�ppppppq ~sq ~ �   Guq ~ �   sq ~ �t !sq ~ �t diametroJoelhosq ~ �t .equals("")ppppppq ~ Csq ~ E�s�}*�v$f�n��O�  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~#q ~#q ~psq ~ q  �bppppq ~#q ~#psq ~ h  �bppppq ~#q ~#psq ~ v  �bppppq ~#q ~#psq ~ z  �bppppq ~#q ~#pppsq ~ ~ppppq ~ppppppppppppt Diámetro de la rodillasq ~ P  �b           �     8pq ~ q ~�ppppppq ~sq ~ �   Huq ~ �   sq ~ �t 	showOsseoppppppq ~ Csq ~ E�!y����A�J?G�  �bppppppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~3q ~3q ~-psq ~ q  �bppppq ~3q ~3psq ~ h  �bppppq ~3q ~3psq ~ v  �bppppq ~3q ~3psq ~ z  �bppppq ~3q ~3pppsq ~ ~ppppq ~-ppppppppppppt Diâmetros ósseos (mm)sq ~ P  �b           �     �pq ~ q ~�ppppppq ~sq ~ �   Iuq ~ �   sq ~ �t !sq ~ �t diametroTornozelosq ~ �t .equals("")ppppppq ~ Csq ~ E��������К%M;  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~Eq ~Eq ~;psq ~ q  �bppppq ~Eq ~Epsq ~ h  �bppppq ~Eq ~Epsq ~ v  �bppppq ~Eq ~Epsq ~ z  �bppppq ~Eq ~Epppsq ~ ~ppppq ~;ppppppppppppt Diámetro del tobillosq ~ P  �b           �     }pq ~ q ~�ppppppq ~sq ~ �   Juq ~ �   sq ~ �t !sq ~ �t diametroCotovelosq ~ �t .equals("")ppppppq ~ Csq ~ E����an�z��_V�Er  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~Yq ~Yq ~Opsq ~ q  �bppppq ~Yq ~Ypsq ~ h  �bppppq ~Yq ~Ypsq ~ v  �bppppq ~Yq ~Ypsq ~ z  �bppppq ~Yq ~Ypppsq ~ ~ppppq ~Oppppppppppppt Diámetro del codosq ~ �  �b           [  �   }pq ~ q ~�ppppppq ~sq ~ �   Kuq ~ �   sq ~ �t !sq ~ �t diametroCotovelosq ~ �t .equals("")ppppppq ~ Csq ~ E���6O\�ɗb��-BI  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~mq ~mq ~cpsq ~ q  �bppppq ~mq ~mpsq ~ h  �bppppq ~mq ~mpsq ~ v  �bppppq ~mq ~mpsq ~ z  �bppppq ~mq ~mpppsq ~ ~ppppq ~cpppppppppppp  �b       ppq ~ �sq ~ �   Luq ~ �   sq ~ �t diametroCotoveloppppppppppppppsq ~ �  �b           [  �   �pq ~ q ~�ppppppq ~sq ~ �   Muq ~ �   sq ~ �t !sq ~ �t diametroTornozelosq ~ �t .equals("")ppppppq ~ Csq ~ E��"Xh�����
	BV  �bppppppppppppsq ~ cpsq ~ g  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�q ~zpsq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~zpppppppppppp  �b       ppq ~ �sq ~ �   Nuq ~ �   sq ~ �t diametroTornozeloppppppppppppppxp  �b   �sq ~ �   ?uq ~ �   sq ~ �t showperimetriapppppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�`Ne��6x�>�`�:Os  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~�psq ~ P  �b   )       &       pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��8R���ts+"�zJ�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Composición corporalsq ~J  �b   Z           �   :pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���\oh�ZQ )�A�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~J  �b   Z             :pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��,
�F��a�� +�H�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~J  �b   Z          �   :pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E����Wt��	jQ�H�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~ P  �b           �      :pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�m��C�C�6���Ci  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Peso de grasasq ~ P  �b           �   �   :pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�
��o��lױNE�  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Peso residualsq ~ P  �b           �     :pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�`A�|ƍi�����K�  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Peso muscularsq ~ P  �b           �  �   :pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�4�o���ԟ�>M�  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~q ~q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~q ~psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~q ~pppsq ~ ~ppppq ~�pppppppppppq ~ �t Peso del huesosq ~ �  �b   -        �      Opq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��r97E���V��D�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �  �b        ppq ~ �sq ~ �   Ouq ~ �   sq ~ �t pesoGordurappppppppppppppsq ~ P  �b           �      |pq ~ q ~�sq ~ ;    �   ppppppppq ~ppppq ~ Csq ~ E�L v� FV-��^TA�  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~&q ~&q ~"psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~&q ~&psq ~ h  �bpppsq ~ M?�  q ~&q ~&psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~&q ~&psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~&q ~&pppsq ~ ~ppppq ~"pppppppppppq ~ �t kilossq ~ �  �b   -        �   �   Opq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���F&�oV�>Z�YE^  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~:q ~:q ~7psq ~ q  �bppppq ~:q ~:psq ~ h  �bppppq ~:q ~:psq ~ v  �bppppq ~:q ~:psq ~ z  �bppppq ~:q ~:pppsq ~ ~ppppq ~7pppppppppppq ~ �  �b        ppq ~ �sq ~ �   Puq ~ �   sq ~ �t pesoResidualppppppppppppppsq ~ P  �b           �   �   |pq ~ q ~�sq ~ ;    �   ppppppppq ~ppppq ~ Csq ~ E��l�	Z@��(�O  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~Iq ~Iq ~Epsq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Iq ~Ipsq ~ h  �bpppsq ~ M?�  q ~Iq ~Ipsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Iq ~Ipsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~Iq ~Ipppsq ~ ~ppppq ~Epppppppppppq ~ �t kilossq ~ P  �b           �     |pq ~ q ~�sq ~ ;    �   ppppppppq ~ppppq ~ Csq ~ E�{��j^?6�^@�~F�  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~^q ~^q ~Zpsq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~^q ~^psq ~ h  �bpppsq ~ M?�  q ~^q ~^psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~^q ~^psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~^q ~^pppsq ~ ~ppppq ~Zpppppppppppq ~ �t kilossq ~ �  �b   -        �     Opq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�,!%���b�/�ǷC�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~rq ~rq ~opsq ~ q  �bppppq ~rq ~rpsq ~ h  �bppppq ~rq ~rpsq ~ v  �bppppq ~rq ~rpsq ~ z  �bppppq ~rq ~rpppsq ~ ~ppppq ~opppppppppppq ~ �  �b        ppq ~ �sq ~ �   Quq ~ �   sq ~ �t pesoMuscularppppppppppppppsq ~ �  �b   -        �  �   Opq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��˂�&}�	c)3K�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~}psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~}pppppppppppq ~ �  �b        ppq ~ �sq ~ �   Ruq ~ �   sq ~ �t 	pesoOsseoppppppppppppppsq ~ P  �b           �  �   |pq ~ q ~�sq ~ ;    �   ppppppppq ~ppppq ~ Csq ~ E���p9�&�A�jC)M4  �bppsq ~ MA   ppq ~ _ppppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M    q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bpppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t kilosxp  �b   �pppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~�ppppppq ~ppppq ~ Csq ~ E������!�I����O�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~�psq ~ P  �b   )       &       pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�"ʾZ=o2~9m @{  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Flexibilidadsq ~J  �b   d          �   6pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�>5�	����GAwiH�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~ P  �b           }  �   8pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�2/�˧gM�(\CIC
  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Tabla de Referenciasq ~ P  �b           }  �   Hpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���ø\���
!d�XH�  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Débil: -24sq ~ P  �b           }  �   Xpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���4��zj�#:��TOQ  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Regular: 24 - 28sq ~ P  �b           }  �   hpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��Z����G$Oz�/yOa  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Media: 29 - 33sq ~ P  �b           }  �   xpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��7����.Z'�lD
O   �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Buena: 34 - 38sq ~ P  �b           }  �   �pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�:�BVv�nT�K �EH  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Excelente: + 38sq ~ P  �b           �      8pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��ш�u�l���˙NR  �bppsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �t Banco de Wells y Dilonsq ~ P  �b           Z      Rpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��+���}z�lM��YM	  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �t Alcance máximo:sq ~ P  �b           M      bpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��w&�?�ѵO��nH�  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �t Clasificación:sq ~ P  �b           M      rpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�|~WNIk�\����O�  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~%q ~%q ~"psq ~ q  �bppppq ~%q ~%psq ~ h  �bppppq ~%q ~%psq ~ v  �bppppq ~%q ~%psq ~ z  �bppppq ~%q ~%pppsq ~ ~ppppq ~"pppppppppppq ~ �t Observaciones:sq ~ �  �b              ]   Rpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�[�A<��x޹���GA  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~/q ~/q ~-psq ~ q  �bppppq ~/q ~/psq ~ h  �bppppq ~/q ~/psq ~ v  �bppppq ~/q ~/psq ~ z  �bppppq ~/q ~/pppsq ~ ~ppppq ~-pppppppppppq ~ �  �b        ppq ~ �sq ~ �   Tuq ~ �   sq ~ �t 
alcanceMaximoppppppppppppppsq ~ �  �b           �   P   bpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��W�v��/�(�$��I5  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~<q ~<q ~:psq ~ q  �bppppq ~<q ~<psq ~ h  �bppppq ~<q ~<psq ~ v  �bppppq ~<q ~<psq ~ z  �bppppq ~<q ~<pppsq ~ ~ppppq ~:pppppppppppq ~ �  �b        ppq ~ �sq ~ �   Uuq ~ �   sq ~ �t classificacaoFlexibilidadeppppppppppppppsq ~ �  �b          �      �pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�
�R����?$���(@�  �bpppppq ~�ppppppsq ~ cpsq ~ g  �bppppq ~Iq ~Iq ~Gpsq ~ q  �bppppq ~Iq ~Ipsq ~ h  �bppppq ~Iq ~Ipsq ~ v  �bppppq ~Iq ~Ipsq ~ z  �bppppq ~Iq ~Ipppsq ~ ~ppppq ~Gpppppppppppq ~
  �b        ppq ~ �sq ~ �   Vuq ~ �   sq ~ �t obsFlexibilidadeppppppppppppppxp  �b   �sq ~ �   Suq ~ �   sq ~ �t showflexibilidadepppppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~Xppppppq ~ppppq ~ Csq ~ E��}�j�hG��[�LL�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~Zpsq ~ P  �b   )       &       pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E�ܐd̀c�¢8F�Gh  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~cq ~cq ~`q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~cq ~cpsq ~ h  �bppppq ~cq ~cpsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~cq ~cpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~cq ~cpppsq ~ ~ppppq ~`pppppppppppq ~ �t Evaluación Posturalsq ~ P  �b           M      �pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E�S����j.���hO�  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~vq ~vq ~spsq ~ q  �bppppq ~vq ~vpsq ~ h  �bppppq ~vq ~vpsq ~ v  �bppppq ~vq ~vpsq ~ z  �bppppq ~vq ~vpppsq ~ ~ppppq ~spppppppppppq ~ �t Visión lateral:sq ~ �  �b          "      �pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E���Hz���N΄"'sE�  �bpppppq ~�ppppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~~psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~~pppppppppppq ~
  �b        ppq ~ �sq ~ �   Xuq ~ �   sq ~ �t visaoLateralppppppppppppppsq ~ �  �b          "      �pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E����b�uߜ�)�m>H�  �bpppppq ~�ppppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~
  �b        ppq ~ �sq ~ �   Yuq ~ �   sq ~ �t 	posteriorppppppppppppppsq ~ P  �b           M      �pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E����M�*G�G�  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Posterior:sq ~ P  �b           M     pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E��(h����x�nA�F�  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 	Anterior:sq ~ �  �b          "     pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E�� ��&�I�%KE  �bpppppq ~�ppppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~
  �b        ppq ~ �sq ~ �   Zuq ~ �   sq ~ �t anteriorppppppppppppppsq ~ P  �b           M     =pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E���p]�ь�@N�  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Asimetrías:sq ~ P  �b           �     Rpq ~ q ~Xppppppq ~ppppq ~ Csq ~ E���qn�_����'�L}  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Hombros asimétricos:sq ~ �  �b              �  Rpq ~ q ~Xppppppq ~ppppq ~ Csq ~ E��.6O�я�X��G_  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   [uq ~ �   sq ~ �t ombrosAssimetricosppppppppppppppsq ~ �  �b             �  bpq ~ q ~Xppppppq ~ppppq ~ Csq ~ E�6 �j�҂�ص]iYH�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   \uq ~ �   sq ~ �t assimetriaQuadrilppppppppppppppsq ~ P  �b           �     bpq ~ q ~Xppppppq ~ppppq ~ Csq ~ E�����"Î��V�mIE  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Asimetría de cadera:sq ~ P  �b           M     ypq ~ q ~Xppppppq ~ppppq ~ Csq ~ E��0
�R���E�  �bppsq ~ MA   pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t Observaciones:sq ~ �  �b          �     �pq ~ q ~Xppppppq ~ppppq ~ Csq ~ E�xM�7�LZ�	�P"I  �bpppppq ~�ppppppsq ~ cpsq ~ g  �bppppq ~	q ~	q ~	psq ~ q  �bppppq ~	q ~	psq ~ h  �bppppq ~	q ~	psq ~ v  �bppppq ~	q ~	psq ~ z  �bppppq ~	q ~	pppsq ~ ~ppppq ~	pppppppppppq ~
  �b        ppq ~ �sq ~ �   ]uq ~ �   sq ~ �t obsPosturalppppppppppppppsq ~ �  �b   w        V      2pq ~ q ~Xppppppq ~ @sq ~ �   ^uq ~ �   sq ~ �t 	urlFrentesq ~ �t  != nullppppppq ~ Csq ~ E��?�&N��4"A�  w�ppsq ~ G  �bppppq ~	  �b         ppq ~ �sq ~ �   _uq ~ �   sq ~ �t 	urlFrentepppppppppppppsq ~ cpsq ~ g  �bppppq ~	q ~	q ~	psq ~ q  �bppppq ~	q ~	psq ~ h  �bppppq ~	q ~	psq ~ v  �bppppq ~	q ~	psq ~ z  �bppppq ~	q ~	pp~q ~ �t ERRORpppppsq ~ �  �b   w        V   �   2pq ~ q ~Xppppppq ~ @sq ~ �   `uq ~ �   sq ~ �t 
urlDireitasq ~ �t  != nullppppppq ~ Csq ~ E��ɕE����$;�|�J/  w�ppsq ~ G  �bppppq ~	#  �b         ppq ~ �sq ~ �   auq ~ �   sq ~ �t 
urlDireitapppppppppppppsq ~ cpsq ~ g  �bppppq ~	0q ~	0q ~	#psq ~ q  �bppppq ~	0q ~	0psq ~ h  �bppppq ~	0q ~	0psq ~ v  �bppppq ~	0q ~	0psq ~ z  �bppppq ~	0q ~	0ppq ~	!pppppsq ~ �  �b   w        V  �   2pq ~ q ~Xppppppq ~ @sq ~ �   buq ~ �   sq ~ �t urlCostasq ~ �t  != nullppppppq ~ Csq ~ E��e�f�X��Kwd=�Gu  w�ppsq ~ G  �bppppq ~	6  �b         ppq ~ �sq ~ �   cuq ~ �   sq ~ �t urlCostapppppppppppppsq ~ cpsq ~ g  �bppppq ~	Cq ~	Cq ~	6psq ~ q  �bppppq ~	Cq ~	Cpsq ~ h  �bppppq ~	Cq ~	Cpsq ~ v  �bppppq ~	Cq ~	Cpsq ~ z  �bppppq ~	Cq ~	Cppq ~	!pppppsq ~ �  �b   w        V  .   2pq ~ q ~Xppppppq ~ @sq ~ �   duq ~ �   sq ~ �t urlEsquerdasq ~ �t  != nullppppppq ~ Csq ~ E�3L�w��`W�B�J  w�ppsq ~ G  �bppppq ~	I  �b         ppq ~ �sq ~ �   euq ~ �   sq ~ �t urlEsquerdapppppppppppppsq ~ cpsq ~ g  �bppppq ~	Vq ~	Vq ~	Ipsq ~ q  �bppppq ~	Vq ~	Vpsq ~ h  �bppppq ~	Vq ~	Vpsq ~ v  �bppppq ~	Vq ~	Vpsq ~ z  �bppppq ~	Vq ~	Vppq ~	!pppppxp  �b  �sq ~ �   Wuq ~ �   sq ~ �t showposturalpppppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~	`ppppppq ~ppppq ~ Csq ~ E�*��(f����&iO�  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~	bpsq ~ P  �b   )       &       pq ~ q ~	`ppppppq ~ppppq ~ Csq ~ E��J�����p*K�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~	kq ~	kq ~	hq ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~	kq ~	kpsq ~ h  �bppppq ~	kq ~	kpsq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~	kq ~	kpsq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~	kq ~	kpppsq ~ ~ppppq ~	hpppppppppppq ~ �t RMLsq ~ �  �b          p      9pq ~ q ~	`ppppppq ~ppppq ~ Csq ~ E�EVN�܂/	ba(N2  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~	}q ~	}q ~	{psq ~ q  �bppppq ~	}q ~	}psq ~ h  �bppppq ~	}q ~	}psq ~ v  �bppppq ~	}q ~	}psq ~ z  �bppppq ~	}q ~	}ppt htmlsq ~ ~ppppq ~	{pppppppppppq ~ �  �b        ppq ~ �sq ~ �   guq ~ �   sq ~ �t '"<b>RML de brazos - Flexiones:</b> " + sq ~ �t rmlBracoppppppppppppppsq ~ �  �b          �      Mpq ~ q ~	`ppppppq ~ppppq ~ Csq ~ E��!�v�kG��=B*  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~	�q ~	�q ~	�psq ~ q  �bppppq ~	�q ~	�psq ~ h  �bppppq ~	�q ~	�psq ~ v  �bppppq ~	�q ~	�psq ~ z  �bppppq ~	�q ~	�ppt htmlsq ~ ~ppppq ~	�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   huq ~ �   sq ~ �t *"<b>RML de abdomen - Abdominales:</b> " + sq ~ �t 
rmlAbdomenppppppppppppppxp  �b   ksq ~ �   fuq ~ �   sq ~ �t showrmlpppppppsq ~ sq ~    w   sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~	�ppppppq ~ppppq ~ Csq ~ E��������̤*��F^  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~	�psq ~ P  �b   )       &       pq ~ q ~	�ppppppq ~ppppq ~ Csq ~ E���3T��ȳ��E�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~	�q ~	�q ~	�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~	�q ~	�psq ~ h  �bppppq ~	�q ~	�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~	�q ~	�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~	�q ~	�pppsq ~ ~ppppq ~	�pppppppppppq ~ �t Vo2Maxxp  �b   /sq ~ �   iuq ~ �   sq ~ �t 
showvo2maxsq ~ �t  || sq ~ �t 
showQueenspppsr +net.sf.jasperreports.engine.JRPropertiesMap      '� L baseq ~ L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xppsq ~    w   t com.jaspersoft.studio.layoutxsr java.util.HashMap���`� F 
loadFactorI 	thresholdxp?@     w      q ~	�t .com.jaspersoft.studio.editor.layout.FreeLayoutxpppsq ~ sq ~    w   sq ~ P  �b           �       pq ~ q ~	�ppppppq ~sq ~ �   kuq ~ �   sq ~ �t showventilometriappppppq ~ Csq ~ E��������(*�N�  �bppsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~	�q ~	�q ~	�psq ~ q  �bppppq ~	�q ~	�psq ~ h  �bppppq ~	�q ~	�psq ~ v  �bppppq ~	�q ~	�psq ~ z  �bppppq ~	�q ~	�pppsq ~ ~ppppq ~	�pppppppppppq ~ �t Ventilometria VO2 sq ~ P  �b           /      pq ~ q ~	�ppppppq ~sq ~ �   luq ~ �   sq ~ �t showventilometriapppppp~q ~ Bt ELEMENT_GROUP_HEIGHTsq ~ E��� �6:_�����M�  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~	�q ~	�q ~	�psq ~ q  �bppppq ~	�q ~	�psq ~ h  �bppppq ~	�q ~	�psq ~ v  �bppppq ~	�q ~	�psq ~ z  �bppppq ~	�q ~	�pppsq ~ ~ppppq ~	�pppppppppppq ~ �t Vo2 Max:sq ~ �  �b              3   pq ~ q ~	�ppppppq ~sq ~ �   muq ~ �   sq ~ �t showventilometriappppppq ~ Csq ~ E�q�m
p�:�qۦn�J�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~	�q ~	�q ~	�psq ~ q  �bppppq ~	�q ~	�psq ~ h  �bppppq ~	�q ~	�psq ~ v  �bppppq ~	�q ~	�psq ~ z  �bppppq ~	�q ~	�pppsq ~ ~ppppq ~	�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   nuq ~ �   sq ~ �t vo2ppppppppppppppsq ~ �  �b           �   h   )pq ~ q ~	�ppppppq ~sq ~ �   ouq ~ �   sq ~ �t showventilometriappppppq ~ Csq ~ E�K���% ࢓K�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~
q ~
q ~	�psq ~ q  �bppppq ~
q ~
psq ~ h  �bppppq ~
q ~
psq ~ v  �bppppq ~
q ~
psq ~ z  �bppppq ~
q ~
pppsq ~ ~ppppq ~	�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   puq ~ �   sq ~ �t limiar1ppppppppppppppsq ~ P  �b           d      )pq ~ q ~	�ppppppq ~sq ~ �   quq ~ �   sq ~ �t showventilometriappppppq ~	�sq ~ E���X�qK��q1B  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~
q ~
q ~
psq ~ q  �bppppq ~
q ~
psq ~ h  �bppppq ~
q ~
psq ~ v  �bppppq ~
q ~
psq ~ z  �bppppq ~
q ~
pppsq ~ ~ppppq ~
pppppppppppq ~ �t Limiar ventilatório I:sq ~ �  �b           �   h   9pq ~ q ~	�ppppppq ~sq ~ �   ruq ~ �   sq ~ �t showventilometriappppppq ~ Csq ~ E��k���].�\��I�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~
#q ~
#q ~
psq ~ q  �bppppq ~
#q ~
#psq ~ h  �bppppq ~
#q ~
#psq ~ v  �bppppq ~
#q ~
#psq ~ z  �bppppq ~
#q ~
#pppsq ~ ~ppppq ~
pppppppppppq ~ �  �b        ppq ~ �sq ~ �   suq ~ �   sq ~ �t limiar2ppppppppppppppsq ~ P  �b           d      9pq ~ q ~	�ppppppq ~sq ~ �   tuq ~ �   sq ~ �t showventilometriappppppq ~ Csq ~ E�8EgZ_x1�2#A�  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~
5q ~
5q ~
.psq ~ q  �bppppq ~
5q ~
5psq ~ h  �bppppq ~
5q ~
5psq ~ v  �bppppq ~
5q ~
5psq ~ z  �bppppq ~
5q ~
5pppsq ~ ~ppppq ~
.pppppppppppq ~ �t Limiar ventilatório II:sq ~J  �b   `             pq ~ q ~	�ppppppq ~sq ~ �   uuq ~ �   sq ~ �t showAstrandppppppq ~ Csq ~ E�}��������b
�Nv  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~
=  �b q ~Psq ~ P  �b                pq ~ q ~	�ppppppq ~sq ~ �   vuq ~ �   sq ~ �t showAstrandpppppp~q ~ Bt CONTAINER_BOTTOMsq ~ E�#��2��ɤ��~@Q  �bppsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~
Nq ~
Nq ~
Epsq ~ q  �bppppq ~
Nq ~
Npsq ~ h  �bppppq ~
Nq ~
Npsq ~ v  �bppppq ~
Nq ~
Npsq ~ z  �bppppq ~
Nq ~
Npppsq ~ ~ppppq ~
Epppppppppppq ~ �t $Protocolo Astrand en cicloergómetrosq ~ �  �b           d  �   pq ~ q ~	�ppppppq ~ @sq ~ �   wuq ~ �   sq ~ �t showAstrandppppsq ~	�psq ~    w   t  com.jaspersoft.studio.unit.widtht !com.jaspersoft.studio.unit.heightxsq ~	�?@     w      q ~
]t pxq ~
^t pxxpq ~ Csq ~ E��⮨����L}BQ�D�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~
cq ~
cq ~
Vpsq ~ q  �bppppq ~
cq ~
cpsq ~ h  �bppppq ~
cq ~
cpsq ~ v  �bppppq ~
cq ~
cpsq ~ z  �bppppq ~
cq ~
cpppsq ~ ~ppppq ~
Vpppppppppppq ~ �  �b        ppq ~ �sq ~ �   xuq ~ �   sq ~ �t frequenciaAstrandppppppppppppppsq ~ P  �b           d     pq ~ q ~	�ppppppq ~sq ~ �   yuq ~ �   sq ~ �t showAstrandppppppq ~	�sq ~ E�c6�u5�ԋ��TK�  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~
uq ~
uq ~
npsq ~ q  �bppppq ~
uq ~
upsq ~ h  �bppppq ~
uq ~
upsq ~ v  �bppppq ~
uq ~
upsq ~ z  �bppppq ~
uq ~
upppsq ~ ~ppppq ~
npppppppppppq ~ �t Frecuencia cardiaca:sq ~ �  �b           �  �   *pq ~ q ~	�ppppppq ~ @sq ~ �   zuq ~ �   sq ~ �t showAstrandppppsq ~	�psq ~    w   t  com.jaspersoft.studio.unit.widtht !com.jaspersoft.studio.unit.heightxsq ~	�?@     w      q ~
�t pxq ~
�t pxxpq ~ Csq ~ E��K�����{��;�Az  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~
�q ~
�q ~
}psq ~ q  �bppppq ~
�q ~
�psq ~ h  �bppppq ~
�q ~
�psq ~ v  �bppppq ~
�q ~
�psq ~ z  �bppppq ~
�q ~
�pppsq ~ ~ppppq ~
}pppppppppppq ~ �  �b        ppq ~ �sq ~ �   {uq ~ �   sq ~ �t cargaAstrandppppppppppppppsq ~ P  �b           d     *pq ~ q ~	�ppppppq ~sq ~ �   |uq ~ �   sq ~ �t showAstrandppppppq ~	�sq ~ E�C��)�Tώ���4C�  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~
�q ~
�q ~
�psq ~ q  �bppppq ~
�q ~
�psq ~ h  �bppppq ~
�q ~
�psq ~ v  �bppppq ~
�q ~
�psq ~ z  �bppppq ~
�q ~
�pppsq ~ ~ppppq ~
�pppppppppppq ~ �t Carga:sq ~ �  �b           �  �   :pq ~ q ~	�ppppppq ~ @sq ~ �   }uq ~ �   sq ~ �t showAstrandppppsq ~	�psq ~    w   t  com.jaspersoft.studio.unit.widtht !com.jaspersoft.studio.unit.heightxsq ~	�?@     w      q ~
�t pxq ~
�t pxxpq ~ Csq ~ E��f�7��ζk���'MG  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~
�q ~
�q ~
�psq ~ q  �bppppq ~
�q ~
�psq ~ h  �bppppq ~
�q ~
�psq ~ v  �bppppq ~
�q ~
�psq ~ z  �bppppq ~
�q ~
�pppsq ~ ~ppppq ~
�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   ~uq ~ �   sq ~ �t 
vo2Astrandppppppppppppppsq ~ P  �b           d     :pq ~ q ~	�ppppppq ~sq ~ �   uq ~ �   sq ~ �t showAstrandppppppq ~	�sq ~ E�b��-���g~vx?N�  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~
�q ~
�q ~
�psq ~ q  �bppppq ~
�q ~
�psq ~ h  �bppppq ~
�q ~
�psq ~ v  �bppppq ~
�q ~
�psq ~ z  �bppppq ~
�q ~
�pppsq ~ ~ppppq ~
�pppppppppppq ~ �t VO2:sq ~ �  �b           �  �   Jpq ~ q ~	�ppppppq ~ @sq ~ �   �uq ~ �   sq ~ �t showAstrandppppsq ~	�psq ~    w   t  com.jaspersoft.studio.unit.widtht !com.jaspersoft.studio.unit.heightxsq ~	�?@     w      q ~
�t pxq ~
�t pxxpq ~ Csq ~ E��G�_�/�O�AV  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~
�q ~
�q ~
�psq ~ q  �bppppq ~
�q ~
�psq ~ h  �bppppq ~
�q ~
�psq ~ v  �bppppq ~
�q ~
�psq ~ z  �bppppq ~
�q ~
�pppsq ~ ~ppppq ~
�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t 
vo2MaxAstrandppppppppppppppsq ~ P  �b           d     Jpq ~ q ~	�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t showAstrandppppppq ~	�sq ~ E�e�i:2�A�17RC�  �bppsq ~ MA   pppq ~ �pppppsq ~ cpsq ~ g  �bppppq ~
�q ~
�q ~
�psq ~ q  �bppppq ~
�q ~
�psq ~ h  �bppppq ~
�q ~
�psq ~ v  �bppppq ~
�q ~
�psq ~ z  �bppppq ~
�q ~
�pppsq ~ ~ppppq ~
�pppppppppppq ~ �t VO2 Max:xp  �b   isq ~ �   juq ~ �   sq ~ �t 
showvo2maxpppppppsq ~ sq ~    	w   	sq ~ P  �b               pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showQueensppppppq ~
Jsq ~ E��"�ck�C�.���GV  �bppsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~
�q ~
�q ~
�psq ~ q  �bppppq ~
�q ~
�psq ~ h  �bppppq ~
�q ~
�psq ~ v  �bppppq ~
�q ~
�psq ~ z  �bppppq ~
�q ~
�pppsq ~ ~ppppq ~
�pppppppppppq ~ �t /Protocolo de Queens College (prueba submáxima)sq ~ �  �b           �     pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showQueensppppppq ~ Csq ~ E�Q���{Wh�J��Ml  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~
q ~
q ~psq ~ q  �bppppq ~
q ~
psq ~ h  �bppppq ~
q ~
psq ~ v  �bppppq ~
q ~
psq ~ z  �bppppq ~
q ~
pppsq ~ ~ppppq ~pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t fcQueensppppppppppppppsq ~ �  �b           �     .pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showQueensppppppq ~ Csq ~ E�}�S��?~�?��@�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t 	vo2Queensppppppppppppppsq ~ �  �b           �      pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showvo2maxppppppq ~ Csq ~ E��w�c��P��^��O�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~/q ~/q ~)psq ~ q  �bppppq ~/q ~/psq ~ h  �bppppq ~/q ~/psq ~ v  �bppppq ~/q ~/psq ~ z  �bppppq ~/q ~/pppsq ~ ~ppppq ~)pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t 
testeCampoppppppppppppppsq ~ �  �b           �      Npq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showvo2maxppppppq ~ Csq ~ E���)�-<��t�`N?  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~@q ~@q ~:psq ~ q  �bppppq ~@q ~@psq ~ h  �bppppq ~@q ~@psq ~ v  �bppppq ~@q ~@psq ~ z  �bppppq ~@q ~@pppsq ~ ~ppppq ~:pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t valor3TesteCampoppppppppppppppsq ~ �  �b                .pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showvo2maxppppppq ~ Csq ~ E����o����Ku"D  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~Qq ~Qq ~Kpsq ~ q  �bppppq ~Qq ~Qpsq ~ h  �bppppq ~Qq ~Qpsq ~ v  �bppppq ~Qq ~Qpsq ~ z  �bppppq ~Qq ~Qpppsq ~ ~ppppq ~Kpppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t valor1TesteCampoppppppppppppppsq ~ P  �b           �      pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showvo2maxppppppq ~ Csq ~ E���g1'6�a�q-�L7  �bppsq ~ MA@  pppq ~ bpppppsq ~ cpsq ~ g  �bppppq ~cq ~cq ~\psq ~ q  �bppppq ~cq ~cpsq ~ h  �bppppq ~cq ~cpsq ~ v  �bppppq ~cq ~cpsq ~ z  �bppppq ~cq ~cpppsq ~ ~ppppq ~\pppppppppppq ~ �t Protocolos de Camposq ~ �  �b           �      >pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showvo2maxppppppq ~ Csq ~ E��A�
�ziѻ F@�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~qq ~qq ~kpsq ~ q  �bppppq ~qq ~qpsq ~ h  �bppppq ~qq ~qpsq ~ v  �bppppq ~qq ~qpsq ~ z  �bppppq ~qq ~qpppsq ~ ~ppppq ~kpppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t valor2TesteCampoppppppppppppppsq ~J  �b   `             pq ~ q ~
�ppppppq ~sq ~ �   �uq ~ �   sq ~ �t 
showvo2maxppppppq ~ Csq ~ E���I9\��ty�Il  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~|  �b q ~Pxp  �b   bpppppsq ~ sq ~    
w   
sq ~ ,  �b   )       &       sq ~ ;    ����pppq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���Q��5�1RR=MN  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~�psq ~ P  �b   )       &       pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�y�A�S�Q^GK"  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~�q ~�q ~�q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Somatotiposq ~J  �b   P           �   5pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E���#�"9Rx�
w@�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~J  �b   P          |   5pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�6WN�o��0��B��J�  w�ppsq ~ G  �bsq ~ ;    ����ppppq ~ lpq ~�  �b q ~Psq ~ P  �b           �      5pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�c�A��{_�l�Gq  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Endomorfiasq ~ P  �b           �   �   5pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E������/<��YW��J+  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Mesomorfiasq ~ P  �b           �  �   5pq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�m1R^./e���t�A  �bt Arialpsq ~ MA   ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    ����ppppq ~ lsq ~ M?�  q ~�q ~�q ~�psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ h  �bsq ~ ;    ����pppppsq ~ M?�  q ~�q ~�psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �t 
Ectomorfiasq ~ �  �b   -        �      Jpq ~ q ~�ppppppq ~ �ppppq ~ Csq ~ E����o��Of��f�J�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t 
endomorfiappppppppppppppsq ~ �  �b   -        �   �   Jpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E��$�ۑ�̺�Ό�K�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bppppq ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t 
mesomorfiappppppppppppppsq ~ �  �b   -        �  �   Jpq ~ q ~�ppppppq ~ppppq ~ Csq ~ E�͏�.�s7YjF�  �bppsq ~ MA�  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppq ~ �  �b        ppq ~ �sq ~ �   �uq ~ �   sq ~ �t 
ectomorfiappppppppppppppxp  �b   �sq ~ �   �uq ~ �   sq ~ �t somatotipiapppppppsq ~ sq ~    w   sq ~�  �b          &       6pq ~ q ~ppppppq ~ppppq ~ Csq ~ E���R�1񲇭��B�psq ~ �   �uq ~ �   sq ~ �t comparativopppsq ~ �   �uq ~ �   sq ~ �t 
SUBREPORT_DIRsq ~ �t  + "comparativo.jasper"pppppppppsq ~ ,  �b   )       &       	sq ~ ;    ����pppq ~ q ~ppppppq ~ppppq ~ Csq ~ E���xn
Φ��&�8KC  w�ppsq ~ G  �bsq ~ ;    ����pppppsq ~ M    q ~%psq ~ P  �b   )       &      	pq ~ q ~ppppppq ~ppppq ~ Csq ~ E�/�/R�j�J"i<nB�  �bppsq ~ MA`  ppq ~ _q ~ bpppppsq ~ cpsq ~ g  �bsq ~ ;    �   ppppq ~ lsq ~ M?�  q ~.q ~.q ~+q ~ psq ~ q  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~.q ~.psq ~ h  �bppppq ~.q ~.psq ~ v  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~.q ~.psq ~ z  �bsq ~ ;    �   ppppq ~ lsq ~ M    q ~.q ~.pppsq ~ ~ppppq ~+pppppppppppq ~ �t (Comparación con evaluaciones anterioressq ~�  �b          &       pq ~ q ~ppppppq ~ @ppppq ~ Csq ~ E���Q���O�z�[�0@  �b q ~�xp  �b   Osq ~ �   �uq ~ �   sq ~ �t showcomparacoesppppppppppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      '� I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ [ propertyExpressionst 8[Lnet/sf/jasperreports/engine/DatasetPropertyExpression;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;L uuidq ~ 9[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  �b pppt avaliacao_fisicaur *[Lnet.sf.jasperreports.engine.JRParameter;" �*�`!  xp   �sr 0net.sf.jasperreports.engine.base.JRBaseParameter      '� 
Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L evaluationTimet >Lnet/sf/jasperreports/engine/type/ParameterEvaluationTimeEnum;L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xppppt REPORT_CONTEXTpsq ~	�pppt )net.sf.jasperreports.engine.ReportContextpsq ~Spppt REPORT_PARAMETERS_MAPpsq ~	�pppt 
java.util.Mappsq ~Spppt JASPER_REPORTS_CONTEXTpsq ~	�pppt 0net.sf.jasperreports.engine.JasperReportsContextpsq ~Spppt 
JASPER_REPORTpsq ~	�pppt (net.sf.jasperreports.engine.JasperReportpsq ~Spppt REPORT_CONNECTIONpsq ~	�pppt java.sql.Connectionpsq ~Spppt REPORT_MAX_COUNTpsq ~	�pppt java.lang.Integerpsq ~Spppt REPORT_DATA_SOURCEpsq ~	�pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~Spppt REPORT_SCRIPTLETpsq ~	�pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~Spppt 
REPORT_LOCALEpsq ~	�pppt java.util.Localepsq ~Spppt REPORT_RESOURCE_BUNDLEpsq ~	�pppt java.util.ResourceBundlepsq ~Spppt REPORT_TIME_ZONEpsq ~	�pppt java.util.TimeZonepsq ~Spppt REPORT_FORMAT_FACTORYpsq ~	�pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~Spppt REPORT_CLASS_LOADERpsq ~	�pppt java.lang.ClassLoaderpsq ~Spppt REPORT_URL_HANDLER_FACTORYpsq ~	�pppt  java.net.URLStreamHandlerFactorypsq ~Spppt REPORT_FILE_RESOLVERpsq ~	�pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~Spppt REPORT_TEMPLATESpsq ~	�pppt java.util.Collectionpsq ~Spppt SORT_FIELDSpsq ~	�pppt java.util.Listpsq ~Spppt FILTERpsq ~	�pppt )net.sf.jasperreports.engine.DatasetFilterpsq ~Spppt REPORT_VIRTUALIZERpsq ~	�pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~Spppt IS_IGNORE_PAGINATIONpsq ~	�pppt java.lang.Booleanpsq ~S  pt logoPadraoRelatoriopt logoPadraoRelatoriopsq ~	�pppt java.lang.Stringpsq ~S pppt diametroJoelhopsq ~	�pppt java.lang.Stringpsq ~S pppt 
alcanceMaximopsq ~	�pppt java.lang.Stringpsq ~S  pppt nomeEmpresapsq ~	�pppt java.lang.Stringpsq ~S pppt empresaNomepsq ~	�pppt java.lang.Stringpsq ~S pppt 	pesoAtualpsq ~	�pppt java.lang.Stringpsq ~S pppt empresaSitepsq ~	�pppt java.lang.Stringpsq ~S pppt empresaEnderecopsq ~	�pppt java.lang.Stringpsq ~S pppt 	fotoAlunopsq ~	�pppt java.lang.Stringpsq ~S pppt 	nomeAlunopsq ~	�pppt java.lang.Stringpsq ~S pppt idadepsq ~	�pppt java.lang.Stringpsq ~S pppt 	avaliadorpsq ~	�pppt java.lang.Stringpsq ~S sq ~ �    uq ~ �   sq ~ �t c"C:\\PactoJ\\Sistemas\\treino-tronco\\src\\main\\resources\\br\\com\\pacto\\relatorio\\avaliacao\\"pppppt 
SUBREPORT_DIRpsq ~	�pppt java.lang.Stringpsq ~S pppt 
dataAvaliacaopsq ~	�pppt java.lang.Stringpsq ~S pppt sexopsq ~	�pppt java.lang.Stringpsq ~S pppt contatopsq ~	�pppt java.lang.Stringpsq ~S pppt proximaAvaliacaopsq ~	�pppt java.lang.Stringpsq ~S pppt imcpsq ~	�pppt java.lang.Stringpsq ~S  pppt 
anamneselistapsq ~	�pppt java.util.Listpsq ~S pppt anteriorpsq ~	�pppt java.lang.Stringpsq ~S pppt perimetriaJRpsq ~	�pppt java.lang.Objectpsq ~S pppt 
diametroPunhopsq ~	�pppt java.lang.Stringpsq ~S pppt alturapsq ~	�pppt java.lang.Stringpsq ~S pppt pesopsq ~	�pppt java.lang.Stringpsq ~S pppt resultadoIMCpsq ~	�pppt java.lang.Stringpsq ~S pppt gordurapsq ~	�pppt java.lang.Stringpsq ~S pppt circunferenciapsq ~	�pppt java.lang.Stringpsq ~S pppt circunferenciaResultadopsq ~	�pppt java.lang.Stringpsq ~S pppt imcResultadopsq ~	�pppt java.lang.Stringpsq ~S pppt usuariopsq ~	�pppt java.lang.Stringpsq ~S pppt composicaoResultadopsq ~	�pppt java.lang.Stringpsq ~S pppt horaEmissaopsq ~	�pppt java.lang.Stringpsq ~S pppt classificacaoFlexibilidadepsq ~	�pppt java.lang.Stringpsq ~S pppt percGordurapsq ~	�pppt java.lang.Stringpsq ~S pppt 	percOssospsq ~	�pppt java.lang.Stringpsq ~S pppt percResiduospsq ~	�pppt java.lang.Stringpsq ~S pppt percMusculospsq ~	�pppt java.lang.Stringpsq ~S pppt 
recomendacoespsq ~	�pppt java.lang.Stringpsq ~S pppt objetivosAlunopsq ~	�pppt java.lang.Stringpsq ~S pppt peso1psq ~	�pppt java.lang.Stringpsq ~S  pppt alturaAtualpsq ~	�pppt java.lang.Stringpsq ~S pppt peso2psq ~	�pppt java.lang.Stringpsq ~S pppt 	posteriorpsq ~	�pppt java.lang.Stringpsq ~S pppt vo2psq ~	�pppt java.lang.Stringpsq ~S pppt pressaoArterialpsq ~	�pppt java.lang.Stringpsq ~S pppt peso3psq ~	�pppt java.lang.Stringpsq ~S  pppt freqCardiacapsq ~	�pppt java.lang.Stringpsq ~S pppt 	showOsseopsq ~	�pppt java.lang.Booleanpsq ~S pppt peso4psq ~	�pppt java.lang.Stringpsq ~S pppt 	dataPeso1psq ~	�pppt java.lang.Stringpsq ~S pppt 	dataPeso2psq ~	�pppt java.lang.Stringpsq ~S pppt 	dataPeso3psq ~	�pppt java.lang.Stringpsq ~S pppt 	dataPeso4psq ~	�pppt java.lang.Stringpsq ~S pppt totalDobraspsq ~	�pppt java.lang.Stringpsq ~S pppt 	protocolopsq ~	�pppt java.lang.Stringpsq ~S  pppt 
anamneseJRpsq ~	�pppt java.lang.Objectpsq ~S pppt 
rmlAbdomenpsq ~	�pppt java.lang.Stringpsq ~S pppt ombrosAssimetricospsq ~	�pppt java.lang.Stringpsq ~S pppt assimetriaQuadrilpsq ~	�pppt java.lang.Stringpsq ~S pppt limiar1psq ~	�pppt java.lang.Stringpsq ~S pppt limiar2psq ~	�pppt java.lang.Stringpsq ~S pppt 
testeCampopsq ~	�pppt java.lang.Stringpsq ~S pppt valor2TesteCampopsq ~	�pppt java.lang.Stringpsq ~S pppt valor1TesteCampopsq ~	�pppt java.lang.Stringpsq ~S pppt parqpsq ~	�pppt java.lang.Stringpsq ~S pppt parqJRpsq ~	�pppt java.lang.Objectpsq ~S  pppt dobrasJRpsq ~	�pppt java.lang.Objectpsq ~S pppt pesoGordurapsq ~	�pppt java.lang.Stringpsq ~S pppt showventilometriapsq ~	�pppt java.lang.Booleanpsq ~S pppt 	pesoOsseopsq ~	�pppt java.lang.Stringpsq ~S pppt pesoMuscularpsq ~	�pppt java.lang.Stringpsq ~S pppt visaoLateralpsq ~	�pppt java.lang.Stringpsq ~S pppt pesoResidualpsq ~	�pppt java.lang.Stringpsq ~S pppt obsFlexibilidadepsq ~	�pppt java.lang.Stringpsq ~S pppt obsPosturalpsq ~	�pppt java.lang.Stringpsq ~S pppt rmlBracopsq ~	�pppt java.lang.Stringpsq ~S pppt valor3TesteCampopsq ~	�pppt java.lang.Stringpsq ~S pppt 	urlFrentepsq ~	�pppt java.lang.Stringpsq ~S pppt 
urlDireitapsq ~	�pppt java.lang.Stringpsq ~S pppt urlEsquerdapsq ~	�pppt java.lang.Stringpsq ~S pppt urlCostapsq ~	�pppt java.lang.Stringpsq ~S pppt 
showdobraspsq ~	�pppt java.lang.Booleanpsq ~S pppt showperimetriapsq ~	�pppt java.lang.Booleanpsq ~S pppt showflexibilidadepsq ~	�pppt java.lang.Booleanpsq ~S pppt showposturalpsq ~	�pppt java.lang.Booleanpsq ~S pppt showrmlpsq ~	�pppt java.lang.Booleanpsq ~S pppt 
showvo2maxpsq ~	�pppt java.lang.Booleanpsq ~S pppt showcomparacoespsq ~	�pppt java.lang.Booleanpsq ~S pppt showrecomendacoespsq ~	�pppt java.lang.Booleanpsq ~S pppt showanamnesepsq ~	�pppt java.lang.Booleanpsq ~S pppt showparqpsq ~	�pppt java.lang.Booleanpsq ~S pppt showpesoalturapsq ~	�pppt java.lang.Booleanpsq ~S pppt 
showobjetivospsq ~	�pppt java.lang.Booleanpsq ~S pppt comparativopsq ~	�pppt java.lang.Objectpsq ~S pppt apresentarAssinaturapsq ~	�pppt java.lang.Booleanpsq ~S pppt cargaAstrandpsq ~	�pppt java.lang.Doublepsq ~S pppt 
assinaturapsq ~	�pppt java.lang.Stringpsq ~S pppt 
ectomorfiapsq ~	�pppt java.lang.Stringpsq ~S pppt 
mesomorfiapsq ~	�pppt java.lang.Stringpsq ~S pppt 
vo2MaxAstrandpsq ~	�pppt java.lang.Doublepsq ~S pppt 
endomorfiapsq ~	�pppt java.lang.Stringpsq ~S pppt somatotipiapsq ~	�pppt java.lang.Booleanpsq ~S pppt diametroCotovelopsq ~	�pppt java.lang.Stringpsq ~S pppt 
vo2Astrandpsq ~	�pppt java.lang.Doublepsq ~S pppt diametroTornozelopsq ~	�pppt java.lang.Stringpsq ~S pppt 	vo2Queenspsq ~	�pppt java.lang.Stringpsq ~S pppt 
Parameter1psq ~	�pppt java.lang.Booleanpsq ~S pppt 
showQueenspsq ~	�pppt java.lang.Booleanpsq ~S pppt fcQueenspsq ~	�pppt java.lang.Stringpsq ~S pppt metaGordurapsq ~	�pppt java.lang.Stringpsq ~S pppt showMetapsq ~	�pppt java.lang.Booleanpsq ~S pppt frequenciaAstrandpsq ~	�pppt java.lang.Doublepsq ~S pppt showAstrandpsq ~	�pppt java.lang.Booleanpsq ~	�psq ~    w   t -com.jaspersoft.studio.data.defaultdataadaptert (com.jaspersoft.studio.report.descriptionxsq ~	�?@     w      q ~pt 	Sample DBq ~qt  xpsr ,net.sf.jasperreports.engine.base.JRBaseQuery      '� [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppsq ~ E�������+��#�L`ur )[Lnet.sf.jasperreports.engine.JRVariable;b�|�,�D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      '� I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ 6L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ 6L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  w�   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)pppt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~lpsq ~|  w�   q ~�ppq ~�pppt MASTER_CURRENT_PAGEpq ~�q ~lpsq ~|  w�   q ~�ppq ~�pppt MASTER_TOTAL_PAGESpq ~�q ~lpsq ~|  w�   q ~�ppq ~�ppsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)pppt 
COLUMN_NUMBERp~q ~�t PAGEq ~lpsq ~|  w�   ~q ~�t COUNTsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)ppppq ~�ppsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(0)pppt REPORT_COUNTpq ~�q ~lpsq ~|  w�   q ~�sq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)ppppq ~�ppsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(0)pppt 
PAGE_COUNTpq ~�q ~lpsq ~|  w�   q ~�sq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(1)ppppq ~�ppsq ~ �   uq ~ �   sq ~ �t new java.lang.Integer(0)pppt COLUMN_COUNTp~q ~�t COLUMNq ~lpsq ~|  w�    ~q ~�t NOTHINGsq ~ �   	uq ~ �   sq ~ �t 1ppppq ~�ppsq ~ �   
uq ~ �   sq ~ �t PAGE_NUMBERsq ~ �t  + 1pppt REPORT_PAGEpq ~�t java.lang.Integerp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~Pp~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITsq ~ sq ~    w   sq ~ �  �b          &        pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E����� ;=��B�  �bppsq ~ MA   ppq ~�q ~ bpppppsq ~ cpsq ~ g  �bppppq ~�q ~�q ~�psq ~ q  �bppppq ~�q ~�psq ~ h  �bppppq ~�q ~�psq ~ v  �bppppq ~�q ~�psq ~ z  �bpppsq ~ M?�  q ~�q ~�pppsq ~ ~ppppq ~�pppppppppppq ~ �  �b        pp~q ~ �t AUTOsq ~ �   �uq ~ �   sq ~ �t 
" Usuario: "+sq ~ �t usuariosq ~ �t + " - Fecha: "+sq ~ �t horaEmissaosq ~ �t  +" - Pagina " + sq ~ �t REPORT_PAGEsq ~ �t  + " de " + sq ~ �t PAGE_NUMBERppppppppppppppxp  �b   pppppsq ~ sq ~    w   sq ~ �  �b   -        d        pq ~ q ~�ppppppq ~ @pppp~q ~ Bt RELATIVE_TO_BAND_HEIGHTsq ~ E�.���s8T���B�  w�ppsq ~ G  �bppppq ~�  �b         ppq ~ �sq ~ �   uq ~ �   sq ~ �t logoPadraoRelatorioppppp~q ~ �t LEFTpppppppsq ~ cq ~ psq ~ g  �bppppq ~ q ~ q ~�q ~ psq ~ q  �bppppq ~ q ~ psq ~ h  �bppppq ~ q ~ q ~ psq ~ v  �bppppq ~ q ~ q ~ psq ~ z  �bppppq ~ q ~ ppq ~ �pq ~ �pppsq ~ �  �b          T   n    pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E���q�|�nF��F  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppp  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t empresaNomeppppppppppppppsq ~ �  �b          T   n   pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E���]��R$��BG  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~q ~q ~psq ~ q  �bppppq ~q ~psq ~ h  �bppppq ~q ~psq ~ v  �bppppq ~q ~psq ~ z  �bppppq ~q ~pppsq ~ ~ppppq ~pppppppppppp  �b        ppq ~ �sq ~ �   
uq ~ �   sq ~ �t empresaEnderecoppppppppppppppsq ~ �  �b          T   n   pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E���o�F�{)��`�J�  �bppppppppppppsq ~ cpsq ~ g  �bppppq ~"q ~"q ~ psq ~ q  �bppppq ~"q ~"psq ~ h  �bppppq ~"q ~"psq ~ v  �bppppq ~"q ~"psq ~ z  �bppppq ~"q ~"pppsq ~ ~ppppq ~ pppppppppppp  �b        ppq ~ �sq ~ �   uq ~ �   sq ~ �t empresaSiteppppppppppppppsq ~J  �b   -           g    sq ~ ;    ����pppq ~ q ~�sq ~ ;    ����ppppppppq ~ @ppppq ~ Csq ~ E��n���"�D�k�Ot  w�ppsq ~ G  �bppppq ~-  �b q ~Psq ~J  �b          &       .pq ~ q ~�ppppppq ~ @ppppq ~ Csq ~ E�����p��#�NH�  w�ppsq ~ G  �bppppq ~2  �b q ~Pxp  �b   /ppppp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICAL~r 0net.sf.jasperreports.engine.type.SectionTypeEnum          xq ~ t BANDpppppsr 6net.sf.jasperreports.engine.design.JRReportCompileData      '� L crosstabCompileDataq ~	�L datasetCompileDataq ~	�L mainDatasetCompileDataq ~ xpsq ~	�?@      w       xsq ~	�?@      w       xur [B���T�  xp  p�����   .S  %avaliacao_fisica_1599226701503_784073  ,net/sf/jasperreports/engine/fill/JREvaluator parameter_vo2Astrand 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_empresaNome parameter_showrecomendacoes parameter_apresentarAssinatura parameter_percGordura parameter_comparativo parameter_showventilometria parameter_showcomparacoes parameter_peso2 parameter_valor2TesteCampo parameter_peso1 parameter_peso4 parameter_peso3 parameter_percOssos parameter_rmlAbdomen parameter_peso parameter_visaoLateral parameter_posterior parameter_freqCardiaca parameter_fotoAluno parameter_cargaAstrand parameter_metaGordura parameter_showobjetivos parameter_pesoResidual parameter_pressaoArterial parameter_somatotipia parameter_REPORT_FILE_RESOLVER parameter_empresaSite parameter_frequenciaAstrand parameter_avaliador parameter_usuario $parameter_REPORT_URL_HANDLER_FACTORY parameter_diametroPunho parameter_valor3TesteCampo parameter_fcQueens parameter_percMusculos parameter_REPORT_CONNECTION parameter_testeCampo parameter_rmlBraco parameter_pesoOsseo parameter_obsPostural parameter_pesoMuscular parameter_JASPER_REPORT parameter_REPORT_TIME_ZONE parameter_REPORT_MAX_COUNT parameter_showrml parameter_anterior parameter_circunferencia parameter_idade parameter_showOsseo parameter_ectomorfia parameter_urlEsquerda parameter_logoPadraoRelatorio parameter_nomeAluno parameter_objetivosAluno parameter_urlFrente parameter_showMeta parameter_showvo2max parameter_obsFlexibilidade parameter_mesomorfia parameter_REPORT_CLASS_LOADER parameter_REPORT_VIRTUALIZER parameter_urlDireita parameter_vo2Queens $parameter_classificacaoFlexibilidade parameter_diametroJoelho parameter_showanamnese parameter_SUBREPORT_DIR parameter_gordura parameter_alcanceMaximo parameter_protocolo parameter_proximaAvaliacao parameter_parqJR parameter_dataAvaliacao parameter_alturaAtual parameter_REPORT_PARAMETERS_MAP 
parameter_vo2 parameter_REPORT_DATA_SOURCE parameter_urlCosta parameter_showperimetria parameter_pesoGordura parameter_contato parameter_endomorfia parameter_anamneseJR parameter_REPORT_LOCALE parameter_recomendacoes parameter_showdobras parameter_showQueens parameter_showpostural  parameter_JASPER_REPORTS_CONTEXT parameter_totalDobras parameter_nomeEmpresa parameter_REPORT_FORMAT_FACTORY parameter_altura parameter_showparq parameter_perimetriaJR 
parameter_imc parameter_assinatura parameter_dataPeso4 parameter_parq parameter_dataPeso3 parameter_composicaoResultado parameter_REPORT_TEMPLATES parameter_dataPeso2 parameter_dataPeso1 parameter_percResiduos parameter_REPORT_SCRIPTLET parameter_imcResultado parameter_assimetriaQuadril parameter_pesoAtual parameter_showpesoaltura  parameter_REPORT_RESOURCE_BUNDLE parameter_limiar2 !parameter_circunferenciaResultado parameter_limiar1 parameter_SORT_FIELDS parameter_showAstrand parameter_IS_IGNORE_PAGINATION parameter_diametroTornozelo parameter_Parameter1 parameter_FILTER parameter_ombrosAssimetricos parameter_valor1TesteCampo parameter_dobrasJR parameter_empresaEndereco parameter_anamneselista parameter_horaEmissao parameter_vo2MaxAstrand parameter_resultadoIMC parameter_showflexibilidade parameter_diametroCotovelo parameter_REPORT_CONTEXT parameter_sexo variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_MASTER_CURRENT_PAGE variable_MASTER_TOTAL_PAGES variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT variable_REPORT_PAGE <init> ()V Code
  � � �	  �  	  �  	  �  	  � 	 	  � 
 	  �  	  �  	  � 
 	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �  	  �   	  � ! 	  � " 	  � # 	  � $ 	  � % 	  � & 	  � ' 	  � ( 	  � ) 	  � * 	  � + 	  � , 	  � - 	  � . 	  � / 	  � 0 	  � 1 	  � 2 	  � 3 	  � 4 	  � 5 	  � 6 	  � 7 	  � 8 	   9 	  : 	  ; 	  < 	  = 	 
 > 	  ? 	  @ 	  A 	  B 	  C 	  D 	  E 	  F 	  G 	  H 	   I 	 " J 	 $ K 	 & L 	 ( M 	 * N 	 , O 	 . P 	 0 Q 	 2 R 	 4 S 	 6 T 	 8 U 	 : V 	 < W 	 > X 	 @ Y 	 B Z 	 D [ 	 F \ 	 H ] 	 J ^ 	 L _ 	 N ` 	 P a 	 R b 	 T c 	 V d 	 X e 	 Z f 	 \ g 	 ^ h 	 ` i 	 b j 	 d k 	 f l 	 h m 	 j n 	 l o 	 n p 	 p q 	 r r 	 t s 	 v t 	 x u 	 z v 	 | w 	 ~ x 	 � y 	 � z 	 � { 	 � | 	 � } 	 � ~ 	 �  	 � � 	 � � 	 � � 	 � � 	 � � 	 � � 	 � � 	 � � 	 � � 	 � � 	 � � 	 � � �	 � � �	 � � �	 � � �	 � � �	 � � �	 � � �	 � � � LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V
 ��� 
initParams (Ljava/util/Map;)V
 ��� 
initFields
 ��� initVars� 
vo2Astrand��� 
java/util/Map�� get &(Ljava/lang/Object;)Ljava/lang/Object;� 0net/sf/jasperreports/engine/fill/JRFillParameter� empresaNome� showrecomendacoes� apresentarAssinatura� percGordura� comparativo� showventilometria� showcomparacoes� peso2� valor2TesteCampo� peso1� peso4� peso3� 	percOssos� 
rmlAbdomen� peso� visaoLateral� 	posterior� freqCardiaca� 	fotoAluno� cargaAstrand� metaGordura� 
showobjetivos� pesoResidual� pressaoArterial� somatotipia� REPORT_FILE_RESOLVER� empresaSite frequenciaAstrand 	avaliador usuario REPORT_URL_HANDLER_FACTORY	 
diametroPunho valor3TesteCampo
 fcQueens percMusculos REPORT_CONNECTION 
testeCampo rmlBraco 	pesoOsseo obsPostural pesoMuscular 
JASPER_REPORT REPORT_TIME_ZONE! REPORT_MAX_COUNT# showrml% anterior' circunferencia) idade+ 	showOsseo- 
ectomorfia/ urlEsquerda1 logoPadraoRelatorio3 	nomeAluno5 objetivosAluno7 	urlFrente9 showMeta; 
showvo2max= obsFlexibilidade? 
mesomorfiaA REPORT_CLASS_LOADERC REPORT_VIRTUALIZERE 
urlDireitaG 	vo2QueensI classificacaoFlexibilidadeK diametroJoelhoM showanamneseO 
SUBREPORT_DIRQ gorduraS 
alcanceMaximoU 	protocoloW proximaAvaliacaoY parqJR[ 
dataAvaliacao] alturaAtual_ REPORT_PARAMETERS_MAPa vo2c REPORT_DATA_SOURCEe urlCostag showperimetriai pesoGordurak contatom 
endomorfiao 
anamneseJRq 
REPORT_LOCALEs 
recomendacoesu 
showdobrasw 
showQueensy showpostural{ JASPER_REPORTS_CONTEXT} totalDobras nomeEmpresa� REPORT_FORMAT_FACTORY� altura� showparq� perimetriaJR� imc� 
assinatura� 	dataPeso4� parq
 ��� initParams1� 	dataPeso3� composicaoResultado� REPORT_TEMPLATES� 	dataPeso2� 	dataPeso1� percResiduos� REPORT_SCRIPTLET� imcResultado� assimetriaQuadril� 	pesoAtual� showpesoaltura� REPORT_RESOURCE_BUNDLE� limiar2� circunferenciaResultado� limiar1� SORT_FIELDS� showAstrand� IS_IGNORE_PAGINATION� diametroTornozelo� 
Parameter1� FILTER� ombrosAssimetricos� valor1TesteCampo� dobrasJR� empresaEndereco� 
anamneselista� horaEmissao� 
vo2MaxAstrand� resultadoIMC� showflexibilidade� diametroCotovelo� REPORT_CONTEXT� sexo� PAGE_NUMBER� /net/sf/jasperreports/engine/fill/JRFillVariable� MASTER_CURRENT_PAGE� MASTER_TOTAL_PAGES� 
COLUMN_NUMBER� REPORT_COUNT� 
PAGE_COUNT� COLUMN_COUNT� REPORT_PAGE evaluate (I)Ljava/lang/Object; 
Exceptions� java/lang/Throwable� UC:\PactoJ\Sistemas\treino-tronco\src\main\resources\br\com\pacto\relatorio\avaliacao\� java/lang/Integer
�� �� (I)V
���� getValue ()Ljava/lang/Object;
���� intValue ()I
��� java/lang/String� java/lang/StringBuffer 	  Grasa: 
� � (Ljava/lang/String;)V
� append ,(Ljava/lang/String;)Ljava/lang/StringBuffer;
�
 toString ()Ljava/lang/String;   Residuo:  
  Huesos:    Musculos:  java/lang/Boolean
� valueOf &(Ljava/lang/Object;)Ljava/lang/String; anamnese.jasper  Protocolo:  
dobras.jasper  perimetria.jasper"  
�$%& equals (Ljava/lang/Object;)Z
( �) (Z)V
 +,� 	evaluate1. "<b>RML de brazos - Flexiones:</b> 0 %<b>RML de abdomen - Abdominales:</b> 
234 booleanValue ()Z6 java/lang/Double8 comparativo.jasper: 
 Usuario: < 
 - Fecha: > 
 - Pagina 
�@A ,(Ljava/lang/Object;)Ljava/lang/StringBuffer;C  de  evaluateOld
�FG� getOldValue
 IJ� evaluateOld1 evaluateEstimated
�MN� getEstimatedValue
 PQ� evaluateEstimated1 
SourceFile !     �                 	     
               
                                                                                                     !     "     #     $     %     &     '     (     )     *     +     ,     -     .     /     0     1     2     3     4     5     6     7     8     9     :     ;     <     =     >     ?     @     A     B     C     D     E     F     G     H     I     J     K     L     M     N     O     P     Q     R     S     T     U     V     W     X     Y     Z     [     \     ]     ^     _     `     a     b     c     d     e     f     g     h     i     j     k     l     m     n     o     p     q     r     s     t     u     v     w     x     y     z     {     |     }     ~          �     �     �     �     �     �     �     �     �     �     �     � �    � �    � �    � �    � �    � �    � �    � �     � �  �      �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*� �*�*�*�*�*�	*�*�
*�*�*�*�*�*�*�*�*�*�!*�#*�%*�'*�)*�+*�-*�/*�1*�3*�5*�7*�9*�;*�=*�?*�A*�C*�E*�G*�I*�K*�M*�O*�Q*�S*�U*�W*�Y*�[*�]*�_*�a*�c*�e*�g*�i*�k*�m*�o*�q*�s*�u*�w*�y*�{*�}*�*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*��*���   �  > �      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1 � 2 � 3 � 4 � 5 � 6 � 7 � 8 � 9 � : � ; � < � = � > � ? � @ � A � B � C � D � E � F � G � H � I � J � K L M
 N O P Q! R& S+ T0 U5 V: W? XD YI ZN [S \X ]] ^b _g `l aq bv c{ d� e� f� g� h� i� j� k� l� m� n� o� p� q� r� s� t� u� v� w� x� y� z� {� |� }� ~  � � � � �  �% �* �/ �4 �9 �> �C �H �M �R �W �\ �a �f �k �p �u �z � �� �� �� �� �� �� �� �� �� �� �� �� �� ��  ��  �   4     *+��*,��*-���   �       �  � 
 �  � ��  �  �    F*+��� �ȵ �*+ʹ� �ȵ �*+̹� �ȵ �*+ι� �ȵ �*+й� �ȵ �*+ҹ� �ȵ �*+Թ� �ȵ �*+ֹ� �ȵ �*+ع� �ȵ �*+ڹ� �ȵ �*+ܹ� �ȵ �*+޹� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+��� �ȵ �*+��� �ȵ �*+��� �ȵ �*+��� �ȵ �*+��� �ȵ �*+��� �ȵ �*+ �� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+
�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+�� �ȵ �*+ �� �ȵ �*+"�� �ȵ �*+$�� �ȵ �*+&�� �ȵ �*+(�� �ȵ �*+*�� �ȵ �*+,�� �ȵ �*+.�� �ȵ �*+0�� �ȵ*+2�� �ȵ*+4�� �ȵ*+6�� �ȵ*+8�� �ȵ	*+:�� �ȵ*+<�� �ȵ
*+>�� �ȵ*+@�� �ȵ*+B�� �ȵ*+D�� �ȵ*+F�� �ȵ*+H�� �ȵ*+J�� �ȵ*+L�� �ȵ*+N�� �ȵ*+P�� �ȵ!*+R�� �ȵ#*+T�� �ȵ%*+V�� �ȵ'*+X�� �ȵ)*+Z�� �ȵ+*+\�� �ȵ-*+^�� �ȵ/*+`�� �ȵ1*+b�� �ȵ3*+d�� �ȵ5*+f�� �ȵ7*+h�� �ȵ9*+j�� �ȵ;*+l�� �ȵ=*+n�� �ȵ?*+p�� �ȵA*+r�� �ȵC*+t�� �ȵE*+v�� �ȵG*+x�� �ȵI*+z�� �ȵK*+|�� �ȵM*+~�� �ȵO*+��� �ȵQ*+��� �ȵS*+��� �ȵU*+��� �ȵW*+��� �ȵY*+��� �ȵ[*+��� �ȵ]*+��� �ȵ_*+���   �  � f   �  �   � 0 � @ � P � ` � p � � � � � � � � � � � � � � � � �  � �  �0 �@ �P �` �p �� �� �� �� �� �� �� �� �  � �  �0 �@ �P �` �p �� �� �� �� �� �� �� �� �  � �  �0 �@ �P �` �p �� �� �� �� �� �� �� �� �  � �  �0 �@ P`p������	�
� 
 0@P`p��������  0@ E! ��  �  �    *+��� �ȵa*+��� �ȵc*+��� �ȵe*+��� �ȵg*+��� �ȵi*+��� �ȵk*+��� �ȵm*+��� �ȵo*+��� �ȵq*+��� �ȵs*+��� �ȵu*+��� �ȵw*+��� �ȵy*+��� �ȵ{*+��� �ȵ}*+��� �ȵ*+��� �ȵ�*+��� �ȵ�*+��� �ȵ�*+��� �ȵ�*+��� �ȵ�*+��� �ȵ�*+��� �ȵ�*+��� �ȵ�*+ù� �ȵ�*+Ź� �ȵ�*+ǹ� �ȵ�*+ɹ� �ȵ�*+˹� �ȵ�*+͹� �ȵ�*+Ϲ� �ȵ�*+ѹ� �ȵ�*+ӹ� �ȵ��   �   � "  ) *  + 0, @- P. `/ p0 �1 �2 �3 �4 �5 �6 �7 �8 9: ;0<@=P>`?p@�A�B�C�D�E�F�G�H IJ ��  �         �   �      R ��  �   �     �*+չ� �׵�*+ٹ� �׵�*+۹� �׵�*+ݹ� �׵�*+߹� �׵�*+�� �׵�*+�� �׵�*+�� �׵��   �   & 	  Z [  \ 0] @^ P_ `` pa �b �� �    � �  �    �M�  z       c  �  �  �  �  �  �  �  �  �      ,  :  H  V  d  r  �  �  �  �  �  �  �  �  �  �      (  6  T  r  �  �  �  �  �  �  �        ,  :  H  V  a  �  �  �  �  �  �  �  �        ,  :  X  c  �  �  �  �  �  �    8  F  j  x  �  �  �  �    $  2  @  N  \  j  x  �  �  �  �  �  �  �  �  �    "  @  N  l�M�߻�Y��M�ӻ�Y��M�ǻ�Y��M����Y��M����Y��M����Y��M����Y��M����Y��M���Z_��M�r*�������`��Z_��M�W*�����M�I*� �����M�;*������M�-*� ϶���M�*� �����M�*�����M�*� �����M��*� Ӷ���M��*������M��*�+����M��*�'����M��*�Y����M��*� �����M��*� �����M��*�{����M��*�c����M�w*�o����M�i*�S����M�[*� �����M�M��Y �*� �������	M�/��Y
�*�k������	M���Y�*� �������	M���Y�*� ߶�����	M��*� �����M��*� �����M��*� �����M��*� �����M��*�	���M��*� ö���M��*�	���M�s*� Ŷ��M�e*�����M�W*� ����M�I*�C����M�;*����M�-*�?��M�"��Y*���������	M�*�U���M��*�)��M���Y*���������	M��*�_����M��*� ����M��*�[����M��*� ����M��*�s����M��*�-����M�s*� ɶ���M�e*� �����M�W*�E���M�I��Y�*�%������	M�+*����M� ��Y*���������	M��*�7���M��*�W��M���Y*���������	M��*� ٶ���!�#� � �Z_�'M��*� ٶ���M��*� ٶ���!�#� � �Z_�'M�o*�����!�#� � �Z_�'M�K*�����M�=*�����!�#� � �Z_�'M�*� ����M�*������!�#� � �Z_�'M��*������!�#� � �Z_�'M��*������!�#� � �Z_�'M��*������M��*������!�#� � �Z_�'M�m*������M�_*�9����M�Q*� Ƕ���M�C*� ����M�5*� ����M�'*�����M�*�#����M�*�����M� �*�
����M� �*�I���M� �*� �����M� �*� �����M� �*� �����M� �*������M� �*�q����M� �*� ����M� �*������ � �Z_�'M� o*�����M� a*������ � �Z_�'M� C*�����M� 5*�5����� � �Z_�'M� *�5����M� 	*�*M,�   �  2 �  j l�p�q�u�v�z�{�������������������������,�/�:�=�H�K�V�Y�d�g�r�u��������������������������������������������(�+69TWru������� �$�%�)�*�.�/�3489=>!B,C/G:H=LHMKQVRYVaWd[�\�`�a�e�f�j�k�o�p�t�u�y�z�~��������!�,�/�:�=�X�[�c�f���������������������������8�;�F�I�j�m�x�{�������������������$�'�2�5 @CNQ
\_jmx{����#�$�(�)�-�.�2�3�7�8�<�=�A�B�FGK"L%P@QCUNVQZl[o_z`}d�h ,� �    � �  S    �M�  �   d   �   �        =  [  �  �  �  �  �  �  �  �  �  
    &  4  B  P  ^  l  z  �  �  �  �  �  �  �  �  �      "  0  >  L  Z  h  v  �  �  �  �  �  �  �  �  �    
  .*� ������ � �Z_�'M��*� �����M�t*� ���M�f��Y-�*� ������	M�H��Y/�*� �������	M�**�����1� *�G����1� � �Z_�'M��*����M��*� ����M��*� ����M��*� ����M��*�1����M��*� ����M��*�}����M��*� ����M��*� ����M�{*�y����M�m*� ����M�_*�����M�Q*�����M�C*�����M�5*� Ѷ��5M�'*�����M�*�����M�*� ����5M��*�����M��*�����M��*� ����5M��*�����M��*�����M��*�����5M��*�����M��*�G���M��*�G���M�*� ݶ���M�q*�G���M�c*�����M�U*����M�G*� ����M�9*����M�+*� ۶���M�*����M�*������M�*����M� �*����M� �*� �����M� �*����M� �*� ˶��M� �*�=����M� �*�����M� �*� �����M� �*� ����M� �*� ���M� x��Y*�������7��	M� W��Y9�*� ն����;�*�������=�*������?B�*������?�	M,�   �  � n  q s �wx|}��"�=�@�[�^�������������������������������������
�
���&�)�4�7�B�E�P�S�^�a�l�o�z�}�����������������������	�
����	!""%&0'3+>,A0L1O5Z6]:h;k?v@yD�E�I�J�N�O�S�T�X�Y�]�^�b�c�g�h�l�m�qrv
w{.|1��� D� �    � �  �    �M�  z       c  �  �  �  �  �  �  �  �  �      ,  :  H  V  d  r  �  �  �  �  �  �  �  �  �  �      (  6  T  r  �  �  �  �  �  �  �        ,  :  H  V  a  �  �  �  �  �  �  �  �        ,  :  X  c  �  �  �  �  �  �    8  F  j  x  �  �  �  �    $  2  @  N  \  j  x  �  �  �  �  �  �  �  �  �    "  @  N  l�M�߻�Y��M�ӻ�Y��M�ǻ�Y��M����Y��M����Y��M����Y��M����Y��M����Y��M���Z_��M�r*���E���`��Z_��M�W*�����M�I*� �����M�;*������M�-*� ϶���M�*� �����M�*�����M�*� �����M��*� Ӷ���M��*������M��*�+����M��*�'����M��*�Y����M��*� �����M��*� �����M��*�{����M��*�c����M�w*�o����M�i*�S����M�[*� �����M�M��Y �*� �������	M�/��Y
�*�k������	M���Y�*� �������	M���Y�*� ߶�����	M��*� �����M��*� �����M��*� �����M��*� �����M��*�	���M��*� ö���M��*�	���M�s*� Ŷ��M�e*�����M�W*� ����M�I*�C����M�;*����M�-*�?��M�"��Y*���������	M�*�U���M��*�)��M���Y*���������	M��*�_����M��*� ����M��*�[����M��*� ����M��*�s����M��*�-����M�s*� ɶ���M�e*� �����M�W*�E���M�I��Y�*�%������	M�+*����M� ��Y*���������	M��*�7���M��*�W��M���Y*���������	M��*� ٶ���!�#� � �Z_�'M��*� ٶ���M��*� ٶ���!�#� � �Z_�'M�o*�����!�#� � �Z_�'M�K*�����M�=*�����!�#� � �Z_�'M�*� ����M�*������!�#� � �Z_�'M��*������!�#� � �Z_�'M��*������!�#� � �Z_�'M��*������M��*������!�#� � �Z_�'M�m*������M�_*�9����M�Q*� Ƕ���M�C*� ����M�5*� ����M�'*�����M�*�#����M�*�����M� �*�
����M� �*�I���M� �*� �����M� �*� �����M� �*� �����M� �*������M� �*�q����M� �*� ����M� �*������ � �Z_�'M� o*�����M� a*������ � �Z_�'M� C*�����M� 5*�5����� � �Z_�'M� *�5����M� 	*�HM,�   �  2 �  � ���������������������������������������,�/�:�=�H�K�V�Y�d�g�r�u�������������������� ����
�����#($+(6)9-T.W2r3u7�8�<�=�A�B�F�G�K�L�P�Q�U�V�Z[_`de!i,j/n:o=sHtKxVyY}a~d��������������������������������������!�,�/�:�=�X�[�c�f���������������������������8�;�F�I�j�m�x {��	�
�����$'"2#5'@(C,N-Q1\2_6j7m;x<{@�A�E�F�J�K�O�P�T�U�Y�Z�^�_�c�d�h�i�mnr"s%w@xC|N}Q�l�o�z�}��� J� �    � �  S    �M�  �   d   �   �        =  [  �  �  �  �  �  �  �  �  �  
    &  4  B  P  ^  l  z  �  �  �  �  �  �  �  �  �      "  0  >  L  Z  h  v  �  �  �  �  �  �  �  �  �    
  .*� ������ � �Z_�'M��*� �����M�t*� ���M�f��Y-�*� ������	M�H��Y/�*� �������	M�**�����1� *�G����1� � �Z_�'M��*����M��*� ����M��*� ����M��*� ����M��*�1����M��*� ����M��*�}����M��*� ����M��*� ����M�{*�y����M�m*� ����M�_*�����M�Q*�����M�C*�����M�5*� Ѷ��5M�'*�����M�*�����M�*� ����5M��*�����M��*�����M��*� ����5M��*�����M��*�����M��*�����5M��*�����M��*�G���M��*�G���M�*� ݶ���M�q*�G���M�c*�����M�U*����M�G*� ����M�9*����M�+*� ۶���M�*����M�*������M�*����M� �*����M� �*� �����M� �*����M� �*� ˶��M� �*�=����M� �*�����M� �*� �����M� �*� ����M� �*� ���M� x��Y*�������7��	M� W��Y9�*� ն����;�*�������=�*���E��?B�*���E��?�	M,�   �  � n  � � �������"�=�@�[�^�������������������������������������
�
���&�)�4�7�B�E�P�S^aloz
}������ �!�%�&�*�+�/�0�4�5�9�:�>?	CDH"I%M0N3R>SAWLXO\Z]]ahbkfvgyk�l�p�q�u�v�z�{�����������������������
��.�1��� K� �    � �  �    �M�  z       c  �  �  �  �  �  �  �  �  �      ,  :  H  V  d  r  �  �  �  �  �  �  �  �  �  �      (  6  T  r  �  �  �  �  �  �  �        ,  :  H  V  a  �  �  �  �  �  �  �  �        ,  :  X  c  �  �  �  �  �  �    8  F  j  x  �  �  �  �    $  2  @  N  \  j  x  �  �  �  �  �  �  �  �  �    "  @  N  l�M�߻�Y��M�ӻ�Y��M�ǻ�Y��M����Y��M����Y��M����Y��M����Y��M����Y��M���Z_��M�r*���L���`��Z_��M�W*�����M�I*� �����M�;*������M�-*� ϶���M�*� �����M�*�����M�*� �����M��*� Ӷ���M��*������M��*�+����M��*�'����M��*�Y����M��*� �����M��*� �����M��*�{����M��*�c����M�w*�o����M�i*�S����M�[*� �����M�M��Y �*� �������	M�/��Y
�*�k������	M���Y�*� �������	M���Y�*� ߶�����	M��*� �����M��*� �����M��*� �����M��*� �����M��*�	���M��*� ö���M��*�	���M�s*� Ŷ��M�e*�����M�W*� ����M�I*�C����M�;*����M�-*�?��M�"��Y*���������	M�*�U���M��*�)��M���Y*���������	M��*�_����M��*� ����M��*�[����M��*� ����M��*�s����M��*�-����M�s*� ɶ���M�e*� �����M�W*�E���M�I��Y�*�%������	M�+*����M� ��Y*���������	M��*�7���M��*�W��M���Y*���������	M��*� ٶ���!�#� � �Z_�'M��*� ٶ���M��*� ٶ���!�#� � �Z_�'M�o*�����!�#� � �Z_�'M�K*�����M�=*�����!�#� � �Z_�'M�*� ����M�*������!�#� � �Z_�'M��*������!�#� � �Z_�'M��*������!�#� � �Z_�'M��*������M��*������!�#� � �Z_�'M�m*������M�_*�9����M�Q*� Ƕ���M�C*� ����M�5*� ����M�'*�����M�*�#����M�*�����M� �*�
����M� �*�I���M� �*� �����M� �*� �����M� �*� �����M� �*������M� �*�q����M� �*� ����M� �*������ � �Z_�'M� o*�����M� a*������ � �Z_�'M� C*�����M� 5*�5����� � �Z_�'M� *�5����M� 	*�OM,�   �  2 �  � ���������������������������������������,�/�:�=�H�K�V Ydg	r
u��������"�#�'�(�,�-�1�2�6�7�;�<@AEFJ(K+O6P9TTUWYrZu^�_�c�d�h�i�m�n�r�s�w�x�|�}�������!�,�/�:�=�H�K�V�Y�a�d��������������������������������������!�,�/�:�=�X�[�c�f������������	�	�	�		�	
�	�			8	;	F	I	!j	"m	&x	'{	+�	,�	0�	1�	5�	6�	:�	;�	?	@	D$	E'	I2	J5	N@	OC	SN	TQ	X\	Y_	]j	^m	bx	c{	g�	h�	l�	m�	q�	r�	v�	w�	{�	|�	��	��	��	��	��	��	��	��	�	�	�"	�%	�@	�C	�N	�Q	�l	�o	�z	�}	��	� Q� �    � �  S    �M�  �   d   �   �        =  [  �  �  �  �  �  �  �  �  �  
    &  4  B  P  ^  l  z  �  �  �  �  �  �  �  �  �      "  0  >  L  Z  h  v  �  �  �  �  �  �  �  �  �    
  .*� ������ � �Z_�'M��*� �����M�t*� ���M�f��Y-�*� ������	M�H��Y/�*� �������	M�**�����1� *�G����1� � �Z_�'M��*����M��*� ����M��*� ����M��*� ����M��*�1����M��*� ����M��*�}����M��*� ����M��*� ����M�{*�y����M�m*� ����M�_*�����M�Q*�����M�C*�����M�5*� Ѷ��5M�'*�����M�*�����M�*� ����5M��*�����M��*�����M��*� ����5M��*�����M��*�����M��*�����5M��*�����M��*�G���M��*�G���M�*� ݶ���M�q*�G���M�c*�����M�U*����M�G*� ����M�9*����M�+*� ۶���M�*����M�*������M�*����M� �*����M� �*� �����M� �*����M� �*� ˶��M� �*�=����M� �*�����M� �*� �����M� �*� ����M� �*� ���M� x��Y*�������7��	M� W��Y9�*� ն����;�*�������=�*���L��?B�*���L��?�	M,�   �  � n  	� 	� �	�	�	�	�	�	�"	�=	�@	�[	�^	��	��	��	��	��	��	��	��	��	��	��	��	��	��
�
�
�
�





&
)
4
7
B
 E
$P
%S
)^
*a
.l
/o
3z
4}
8�
9�
=�
>�
B�
C�
G�
H�
L�
M�
Q�
R�
V�
W�
[�
\�
`�
a�
e
f	
j
k
o"
p%
t0
u3
y>
zA
~L
O
�Z
�]
�h
�k
�v
�y
��
��
��
��
��
��
��
��
��
��
��
��
��
��
��
��
��
��
�
�
�
�
�.
�1
��
� R    t _1599226701503_784073t 2net.sf.jasperreports.engine.design.JRJavacCompiler