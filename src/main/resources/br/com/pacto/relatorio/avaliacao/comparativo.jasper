�� sr (net.sf.jasperreports.engine.JasperReport      '� L compileDatat Ljava/io/Serializable;L compileNameSuffixt Ljava/lang/String;L 
compilerClassq ~ xr -net.sf.jasperreports.engine.base.JRBaseReport      '� +I PSEUDO_SERIAL_VERSION_UIDI bottomMarginI columnCountI 
columnSpacingI columnWidthZ ignorePaginationZ isFloatColumnFooterZ isSummaryNewPageZ  isSummaryWithPageHeaderAndFooterZ isTitleNewPageI 
leftMarginB orientationI 
pageHeightI 	pageWidthB 
printOrderI rightMarginI 	topMarginB whenNoDataTypeL 
backgroundt $Lnet/sf/jasperreports/engine/JRBand;L columnDirectiont 3Lnet/sf/jasperreports/engine/type/RunDirectionEnum;L columnFooterq ~ L columnHeaderq ~ [ datasetst ([Lnet/sf/jasperreports/engine/JRDataset;L defaultStylet %Lnet/sf/jasperreports/engine/JRStyle;L detailq ~ L 
detailSectiont 'Lnet/sf/jasperreports/engine/JRSection;L formatFactoryClassq ~ L 
importsSett Ljava/util/Set;L languageq ~ L lastPageFooterq ~ L mainDatasett 'Lnet/sf/jasperreports/engine/JRDataset;L nameq ~ L noDataq ~ L orientationValuet 2Lnet/sf/jasperreports/engine/type/OrientationEnum;L 
pageFooterq ~ L 
pageHeaderq ~ L printOrderValuet 1Lnet/sf/jasperreports/engine/type/PrintOrderEnum;L sectionTypet 2Lnet/sf/jasperreports/engine/type/SectionTypeEnum;[ stylest &[Lnet/sf/jasperreports/engine/JRStyle;L summaryq ~ [ 	templatest /[Lnet/sf/jasperreports/engine/JRReportTemplate;L titleq ~ L whenNoDataTypeValuet 5Lnet/sf/jasperreports/engine/type/WhenNoDataTypeEnum;xp  �b             &           J  &          p~r 1net.sf.jasperreports.engine.type.RunDirectionEnum          xr java.lang.Enum          xpt LTRpppppsr .net.sf.jasperreports.engine.base.JRBaseSection      '� [ bandst %[Lnet/sf/jasperreports/engine/JRBand;[ partst %[Lnet/sf/jasperreports/engine/JRPart;xpur %[Lnet.sf.jasperreports.engine.JRBand;��~�ʅ5  xp   sr +net.sf.jasperreports.engine.base.JRBaseBand      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isSplitAllowedL printWhenExpressiont *Lnet/sf/jasperreports/engine/JRExpression;L 
propertiesMapt -Lnet/sf/jasperreports/engine/JRPropertiesMap;L returnValuest Ljava/util/List;L 	splitTypet Ljava/lang/Byte;L splitTypeValuet 0Lnet/sf/jasperreports/engine/type/SplitTypeEnum;xr 3net.sf.jasperreports.engine.base.JRBaseElementGroup      '� L childrenq ~ L elementGroupt ,Lnet/sf/jasperreports/engine/JRElementGroup;xpsr java.util.ArrayListx����a� I sizexp   w   sr 0net.sf.jasperreports.engine.base.JRBaseTextField      '� I PSEUDO_SERIAL_VERSION_UIDI 
bookmarkLevelB evaluationTimeB hyperlinkTargetB 
hyperlinkTypeZ isStretchWithOverflowL anchorNameExpressionq ~ L evaluationGroupt %Lnet/sf/jasperreports/engine/JRGroup;L evaluationTimeValuet 5Lnet/sf/jasperreports/engine/type/EvaluationTimeEnum;L 
expressionq ~ L hyperlinkAnchorExpressionq ~ L hyperlinkPageExpressionq ~ [ hyperlinkParameterst 3[Lnet/sf/jasperreports/engine/JRHyperlinkParameter;L hyperlinkReferenceExpressionq ~ L hyperlinkTooltipExpressionq ~ L hyperlinkWhenExpressionq ~ L isBlankWhenNullt Ljava/lang/Boolean;L 
linkTargetq ~ L linkTypeq ~ L patternq ~ L patternExpressionq ~ xr 2net.sf.jasperreports.engine.base.JRBaseTextElement      '� I PSEUDO_SERIAL_VERSION_UIDL fontNameq ~ L fontSizet Ljava/lang/Integer;L fontsizet Ljava/lang/Float;L horizontalAlignmentq ~  L horizontalAlignmentValuet 6Lnet/sf/jasperreports/engine/type/HorizontalAlignEnum;L horizontalTextAlignt :Lnet/sf/jasperreports/engine/type/HorizontalTextAlignEnum;L isBoldq ~ +L isItalicq ~ +L 
isPdfEmbeddedq ~ +L isStrikeThroughq ~ +L isStyledTextq ~ +L isUnderlineq ~ +L lineBoxt 'Lnet/sf/jasperreports/engine/JRLineBox;L lineSpacingq ~  L lineSpacingValuet 2Lnet/sf/jasperreports/engine/type/LineSpacingEnum;L markupq ~ L 	paragrapht )Lnet/sf/jasperreports/engine/JRParagraph;L pdfEncodingq ~ L pdfFontNameq ~ L rotationq ~  L 
rotationValuet /Lnet/sf/jasperreports/engine/type/RotationEnum;L verticalAlignmentq ~  L verticalAlignmentValuet 4Lnet/sf/jasperreports/engine/type/VerticalAlignEnum;L verticalTextAlignt 8Lnet/sf/jasperreports/engine/type/VerticalTextAlignEnum;xr .net.sf.jasperreports.engine.base.JRBaseElement      '� I PSEUDO_SERIAL_VERSION_UIDI heightZ isPrintInFirstWholeBandZ isPrintRepeatedValuesZ isPrintWhenDetailOverflowsZ isRemoveLineWhenBlankB positionTypeB stretchTypeI widthI xI yL 	backcolort Ljava/awt/Color;L defaultStyleProvidert 4Lnet/sf/jasperreports/engine/JRDefaultStyleProvider;L elementGroupq ~ #L 	forecolorq ~ 8L keyq ~ L modeq ~  L 	modeValuet +Lnet/sf/jasperreports/engine/type/ModeEnum;L parentStyleq ~ L parentStyleNameReferenceq ~ L positionTypeValuet 3Lnet/sf/jasperreports/engine/type/PositionTypeEnum;L printWhenExpressionq ~ L printWhenGroupChangesq ~ (L 
propertiesMapq ~ [ propertyExpressionst 3[Lnet/sf/jasperreports/engine/JRPropertyExpression;L stretchTypeValuet 2Lnet/sf/jasperreports/engine/type/StretchTypeEnum;L uuidt Ljava/util/UUID;xp  �b           �        pq ~ q ~ $pppppp~r 1net.sf.jasperreports.engine.type.PositionTypeEnum          xq ~ t FIX_RELATIVE_TO_TOPpppp~r 0net.sf.jasperreports.engine.type.StretchTypeEnum          xq ~ t 
NO_STRETCHsr java.util.UUID����m�/ J leastSigBitsJ mostSigBitsxp�]��rdש�=9eA�  �bppppppppppppsr .net.sf.jasperreports.engine.base.JRBaseLineBox      '� L 
bottomPaddingq ~ -L 	bottomPent +Lnet/sf/jasperreports/engine/base/JRBoxPen;L boxContainert ,Lnet/sf/jasperreports/engine/JRBoxContainer;L leftPaddingq ~ -L leftPenq ~ IL paddingq ~ -L penq ~ IL rightPaddingq ~ -L rightPenq ~ IL 
topPaddingq ~ -L topPenq ~ Ixppsr 3net.sf.jasperreports.engine.base.JRBaseBoxBottomPen      '�  xr -net.sf.jasperreports.engine.base.JRBaseBoxPen      '� L lineBoxq ~ 1xr *net.sf.jasperreports.engine.base.JRBasePen      '� I PSEUDO_SERIAL_VERSION_UIDL 	lineColorq ~ 8L 	lineStyleq ~  L lineStyleValuet 0Lnet/sf/jasperreports/engine/type/LineStyleEnum;L 	lineWidthq ~ .L penContainert ,Lnet/sf/jasperreports/engine/JRPenContainer;xp  �bsr java.awt.Color���3u F falphaI valueL cst Ljava/awt/color/ColorSpace;[ 	frgbvaluet [F[ fvalueq ~ Txp    �   pppp~r .net.sf.jasperreports.engine.type.LineStyleEnum          xq ~ t SOLIDsr java.lang.Float��ɢ�<�� F valuexr java.lang.Number������  xp    q ~ Kq ~ Kq ~ ?psr 1net.sf.jasperreports.engine.base.JRBaseBoxLeftPen      '�  xq ~ M  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ Kq ~ Kpsq ~ M  �bppppq ~ Kq ~ Kpsr 2net.sf.jasperreports.engine.base.JRBaseBoxRightPen      '�  xq ~ M  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ Kq ~ Kpsr 0net.sf.jasperreports.engine.base.JRBaseBoxTopPen      '�  xq ~ M  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ Kq ~ Kppt htmlsr 0net.sf.jasperreports.engine.base.JRBaseParagraph      '� 
L firstLineIndentq ~ -L 
leftIndentq ~ -L lineSpacingq ~ 2L lineSpacingSizeq ~ .L paragraphContainert 2Lnet/sf/jasperreports/engine/JRParagraphContainer;L rightIndentq ~ -L spacingAfterq ~ -L 
spacingBeforeq ~ -L tabStopWidthq ~ -L tabStopsq ~ xpppppq ~ ?ppppppppppp~r 6net.sf.jasperreports.engine.type.VerticalTextAlignEnum          xq ~ t MIDDLE  �b       pp~r 3net.sf.jasperreports.engine.type.EvaluationTimeEnum          xq ~ t NOWsr 1net.sf.jasperreports.engine.base.JRBaseExpression      '� I id[ chunkst 0[Lnet/sf/jasperreports/engine/JRExpressionChunk;L typet 5Lnet/sf/jasperreports/engine/type/ExpressionTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp   ur 0[Lnet.sf.jasperreports.engine.JRExpressionChunk;mY��iK�U  xp   sr 6net.sf.jasperreports.engine.base.JRBaseExpressionChunk      '� B typeL textq ~ xpt 	descricaoppppppppppppppsq ~ '  �b           P   �    pq ~ q ~ $ppppppq ~ Appppq ~ Dsq ~ F�ݷ��M��8
/�N  �bppsq ~ YA  pp~r 8net.sf.jasperreports.engine.type.HorizontalTextAlignEnum          xq ~ t RIGHTppppppsq ~ Hpsq ~ L  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �q ~ |psq ~ \  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ M  �bppppq ~ �q ~ �psq ~ a  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ e  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �ppt htmlsq ~ jppppq ~ |pppppppppppq ~ n  �b       ppq ~ qsq ~ s   	uq ~ w   sq ~ yt valor1ppppppppppppppsq ~ '  �b           P   �    pq ~ q ~ $ppppppq ~ Appppq ~ Dsq ~ F������m�.�2G4  �bppsq ~ YA  ppq ~ �ppppppsq ~ Hpsq ~ L  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �q ~ �psq ~ \  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ M  �bppppq ~ �q ~ �psq ~ a  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ e  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �ppt htmlsq ~ jppppq ~ �pppppppppppq ~ n  �b       ppq ~ qsq ~ s   
uq ~ w   sq ~ yt valor2ppppppppppppppsq ~ '  �b           P  6    pq ~ q ~ $ppppppq ~ Appppq ~ Dsq ~ F��<ٸ�����%�O�  �bppsq ~ YA  ppq ~ �ppppppsq ~ Hpsq ~ L  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �q ~ �psq ~ \  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ M  �bppppq ~ �q ~ �psq ~ a  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ e  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �ppt htmlsq ~ jppppq ~ �pppppppppppq ~ n  �b       ppq ~ qsq ~ s   uq ~ w   sq ~ yt valor3ppppppppppppppsq ~ '  �b           P  �    pq ~ q ~ $ppppppq ~ Appppq ~ Dsq ~ F�R5�1�*�7"M�  �bppsq ~ YA  ppq ~ �ppppppsq ~ Hpsq ~ L  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �q ~ �psq ~ \  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ M  �bppppq ~ �q ~ �psq ~ a  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ e  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �ppt htmlsq ~ jppppq ~ �pppppppppppq ~ n  �b       ppq ~ qsq ~ s   uq ~ w   sq ~ yt valor4ppppppppppppppsq ~ '  �b           P  �    pq ~ q ~ $ppppppq ~ Appppq ~ Dsq ~ F�^a]�9尰A� ��K	  �bppsq ~ YA  ppq ~ �ppppppsq ~ Hpsq ~ L  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �q ~ �psq ~ \  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ M  �bppppq ~ �q ~ �psq ~ a  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �psq ~ e  �bsq ~ R    �   ppppq ~ Wsq ~ Y    q ~ �q ~ �ppt htmlsq ~ jppppq ~ �pppppppppppq ~ n  �b       ppq ~ qsq ~ s   
uq ~ w   sq ~ yt valor5ppppppppppppppsr +net.sf.jasperreports.engine.base.JRBaseLine      '� I PSEUDO_SERIAL_VERSION_UIDB 	directionL directionValuet 4Lnet/sf/jasperreports/engine/type/LineDirectionEnum;xr 5net.sf.jasperreports.engine.base.JRBaseGraphicElement      '� I PSEUDO_SERIAL_VERSION_UIDL fillq ~  L 	fillValuet +Lnet/sf/jasperreports/engine/type/FillEnum;L linePent #Lnet/sf/jasperreports/engine/JRPen;xq ~ 7  �b          &       pq ~ q ~ $sq ~ R    ����pppppppp~q ~ @t FLOATppppq ~ Dsq ~ F�I��I�$���'�A
  w�ppsq ~ N  �bppppq ~ �  �b ~r 2net.sf.jasperreports.engine.type.LineDirectionEnum          xq ~ t TOP_DOWNxp  �b   pppp~r .net.sf.jasperreports.engine.type.SplitTypeEnum          xq ~ t STRETCHpppt javapsr .net.sf.jasperreports.engine.base.JRBaseDataset      '� I PSEUDO_SERIAL_VERSION_UIDZ isMainB whenResourceMissingType[ fieldst &[Lnet/sf/jasperreports/engine/JRField;L filterExpressionq ~ [ groupst &[Lnet/sf/jasperreports/engine/JRGroup;L nameq ~ [ 
parameterst *[Lnet/sf/jasperreports/engine/JRParameter;L 
propertiesMapq ~ [ propertyExpressionst 8[Lnet/sf/jasperreports/engine/DatasetPropertyExpression;L queryt %Lnet/sf/jasperreports/engine/JRQuery;L resourceBundleq ~ L scriptletClassq ~ [ 
scriptletst *[Lnet/sf/jasperreports/engine/JRScriptlet;[ 
sortFieldst *[Lnet/sf/jasperreports/engine/JRSortField;L uuidq ~ >[ 	variablest )[Lnet/sf/jasperreports/engine/JRVariable;L whenResourceMissingTypeValuet >Lnet/sf/jasperreports/engine/type/WhenResourceMissingTypeEnum;xp  �b ur &[Lnet.sf.jasperreports.engine.JRField;<��N*�p  xp   sr ,net.sf.jasperreports.engine.base.JRBaseField      '� L descriptionq ~ L nameq ~ L 
propertiesMapq ~ [ propertyExpressionsq ~ <L valueClassNameq ~ L valueClassRealNameq ~ xppt valor1sr +net.sf.jasperreports.engine.JRPropertiesMap      '� L baseq ~ L propertiesListq ~ L 
propertiesMapt Ljava/util/Map;xpppppt java.lang.Stringpsq ~pt valor2sq ~ppppt java.lang.Stringpsq ~pt valor3sq ~ppppt java.lang.Stringpsq ~pt valor4sq ~ppppt java.lang.Stringpsq ~pt valor5sq ~ppppt java.lang.Stringpsq ~pt 	descricaosq ~ppppt java.lang.Stringpppt 
Perimetriaur *[Lnet.sf.jasperreports.engine.JRParameter;" �*�`!  xp   sr 0net.sf.jasperreports.engine.base.JRBaseParameter      '� 
Z isForPromptingZ isSystemDefinedL defaultValueExpressionq ~ L descriptionq ~ L evaluationTimet >Lnet/sf/jasperreports/engine/type/ParameterEvaluationTimeEnum;L nameq ~ L nestedTypeNameq ~ L 
propertiesMapq ~ L valueClassNameq ~ L valueClassRealNameq ~ xppppt REPORT_CONTEXTpsq ~pppt )net.sf.jasperreports.engine.ReportContextpsq ~/pppt REPORT_PARAMETERS_MAPpsq ~pppt 
java.util.Mappsq ~/pppt JASPER_REPORTS_CONTEXTpsq ~pppt 0net.sf.jasperreports.engine.JasperReportsContextpsq ~/pppt 
JASPER_REPORTpsq ~pppt (net.sf.jasperreports.engine.JasperReportpsq ~/pppt REPORT_CONNECTIONpsq ~pppt java.sql.Connectionpsq ~/pppt REPORT_MAX_COUNTpsq ~pppt java.lang.Integerpsq ~/pppt REPORT_DATA_SOURCEpsq ~pppt (net.sf.jasperreports.engine.JRDataSourcepsq ~/pppt REPORT_SCRIPTLETpsq ~pppt /net.sf.jasperreports.engine.JRAbstractScriptletpsq ~/pppt 
REPORT_LOCALEpsq ~pppt java.util.Localepsq ~/pppt REPORT_RESOURCE_BUNDLEpsq ~pppt java.util.ResourceBundlepsq ~/pppt REPORT_TIME_ZONEpsq ~pppt java.util.TimeZonepsq ~/pppt REPORT_FORMAT_FACTORYpsq ~pppt .net.sf.jasperreports.engine.util.FormatFactorypsq ~/pppt REPORT_CLASS_LOADERpsq ~pppt java.lang.ClassLoaderpsq ~/pppt REPORT_URL_HANDLER_FACTORYpsq ~pppt  java.net.URLStreamHandlerFactorypsq ~/pppt REPORT_FILE_RESOLVERpsq ~pppt -net.sf.jasperreports.engine.util.FileResolverpsq ~/pppt REPORT_TEMPLATESpsq ~pppt java.util.Collectionpsq ~/pppt SORT_FIELDSpsq ~pppt java.util.Listpsq ~/pppt FILTERpsq ~pppt )net.sf.jasperreports.engine.DatasetFilterpsq ~/pppt REPORT_VIRTUALIZERpsq ~pppt )net.sf.jasperreports.engine.JRVirtualizerpsq ~/pppt IS_IGNORE_PAGINATIONpsq ~pppt java.lang.Booleanpsq ~psq ~ %   
w   
t -com.jaspersoft.studio.data.defaultdataadaptert com.jaspersoft.studio.unit.t %com.jaspersoft.studio.unit.pageHeightt $com.jaspersoft.studio.unit.pageWidtht $com.jaspersoft.studio.unit.topMargint 'com.jaspersoft.studio.unit.bottomMargint %com.jaspersoft.studio.unit.leftMargint &com.jaspersoft.studio.unit.rightMargint &com.jaspersoft.studio.unit.columnWidtht (com.jaspersoft.studio.unit.columnSpacingxsr java.util.HashMap���`� F 
loadFactorI 	thresholdxp?@     w      
q ~�t pixelq ~�t pixelq ~�t New Data Adapter q ~�t pixelq ~�t pixelq ~�t pixelq ~�t pixelq ~�t pixelq ~�t pixelq ~�t pixelxpsr ,net.sf.jasperreports.engine.base.JRBaseQuery      '� [ chunkst +[Lnet/sf/jasperreports/engine/JRQueryChunk;L languageq ~ xppt sqlppppsq ~ F��j��t�5N�͠~JINur )[Lnet.sf.jasperreports.engine.JRVariable;b�|�,�D  xp   sr /net.sf.jasperreports.engine.base.JRBaseVariable      '� I PSEUDO_SERIAL_VERSION_UIDB calculationB 
incrementTypeZ isSystemDefinedB 	resetTypeL calculationValuet 2Lnet/sf/jasperreports/engine/type/CalculationEnum;L 
expressionq ~ L incrementGroupq ~ (L incrementTypeValuet 4Lnet/sf/jasperreports/engine/type/IncrementTypeEnum;L incrementerFactoryClassNameq ~ L incrementerFactoryClassRealNameq ~ L initialValueExpressionq ~ L nameq ~ L 
resetGroupq ~ (L resetTypeValuet 0Lnet/sf/jasperreports/engine/type/ResetTypeEnum;L valueClassNameq ~ L valueClassRealNameq ~ xp  w�   ~r 0net.sf.jasperreports.engine.type.CalculationEnum          xq ~ t SYSTEMpp~r 2net.sf.jasperreports.engine.type.IncrementTypeEnum          xq ~ t NONEppsq ~ s    uq ~ w   sq ~ yt new java.lang.Integer(1)pppt PAGE_NUMBERp~r .net.sf.jasperreports.engine.type.ResetTypeEnum          xq ~ t REPORTq ~Hpsq ~�  w�   q ~�ppq ~�pppt MASTER_CURRENT_PAGEpq ~�q ~Hpsq ~�  w�   q ~�ppq ~�pppt MASTER_TOTAL_PAGESpq ~�q ~Hpsq ~�  w�   q ~�ppq ~�ppsq ~ s   uq ~ w   sq ~ yt new java.lang.Integer(1)pppt 
COLUMN_NUMBERp~q ~�t PAGEq ~Hpsq ~�  w�   ~q ~�t COUNTsq ~ s   uq ~ w   sq ~ yt new java.lang.Integer(1)ppppq ~�ppsq ~ s   uq ~ w   sq ~ yt new java.lang.Integer(0)pppt REPORT_COUNTpq ~�q ~Hpsq ~�  w�   q ~�sq ~ s   uq ~ w   sq ~ yt new java.lang.Integer(1)ppppq ~�ppsq ~ s   uq ~ w   sq ~ yt new java.lang.Integer(0)pppt 
PAGE_COUNTpq ~�q ~Hpsq ~�  w�   q ~�sq ~ s   uq ~ w   sq ~ yt new java.lang.Integer(1)ppppq ~�ppsq ~ s   uq ~ w   sq ~ yt new java.lang.Integer(0)pppt COLUMN_COUNTp~q ~�t COLUMNq ~Hp~r <net.sf.jasperreports.engine.type.WhenResourceMissingTypeEnum          xq ~ t NULLq ~,p~r 0net.sf.jasperreports.engine.type.OrientationEnum          xq ~ t PORTRAITpp~r /net.sf.jasperreports.engine.type.PrintOrderEnum          xq ~ t VERTICAL~r 0net.sf.jasperreports.engine.type.SectionTypeEnum          xq ~ t BANDpppppsr 6net.sf.jasperreports.engine.design.JRReportCompileData      '� L crosstabCompileDataq ~L datasetCompileDataq ~L mainDatasetCompileDataq ~ xpsq ~�?@      w       xsq ~�?@      w       xur [B���T�  xp  �����   . �  Perimetria_1527220399524_869119  ,net/sf/jasperreports/engine/fill/JREvaluator parameter_IS_IGNORE_PAGINATION 2Lnet/sf/jasperreports/engine/fill/JRFillParameter; parameter_REPORT_CONNECTION parameter_FILTER parameter_JASPER_REPORT parameter_REPORT_LOCALE parameter_REPORT_TIME_ZONE parameter_REPORT_TEMPLATES parameter_REPORT_MAX_COUNT parameter_REPORT_SCRIPTLET  parameter_JASPER_REPORTS_CONTEXT parameter_REPORT_FILE_RESOLVER parameter_REPORT_FORMAT_FACTORY parameter_REPORT_PARAMETERS_MAP  parameter_REPORT_RESOURCE_BUNDLE parameter_REPORT_DATA_SOURCE parameter_REPORT_CONTEXT parameter_REPORT_CLASS_LOADER $parameter_REPORT_URL_HANDLER_FACTORY parameter_REPORT_VIRTUALIZER parameter_SORT_FIELDS field_valor5 .Lnet/sf/jasperreports/engine/fill/JRFillField; field_valor4 field_valor1 field_valor3 field_valor2 field_descricao variable_PAGE_NUMBER 1Lnet/sf/jasperreports/engine/fill/JRFillVariable; variable_MASTER_CURRENT_PAGE variable_MASTER_TOTAL_PAGES variable_COLUMN_NUMBER variable_REPORT_COUNT variable_PAGE_COUNT variable_COLUMN_COUNT <init> ()V Code
  - ) *	  /  	  1  	  3  	  5 	 	  7 
 	  9  	  ;  	  = 
 	  ?  	  A  	  C  	  E  	  G  	  I  	  K  	  M  	  O  	  Q  	  S  	  U  	  W  	  Y  	  [  	  ]  	  _  	  a   	  c ! "	  e # "	  g $ "	  i % "	  k & "	  m ' "	  o ( " LineNumberTable customizedInit 0(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;)V
  t u v 
initParams (Ljava/util/Map;)V
  x y v 
initFields
  { | v initVars ~ IS_IGNORE_PAGINATION � � � 
java/util/Map � � get &(Ljava/lang/Object;)Ljava/lang/Object; � 0net/sf/jasperreports/engine/fill/JRFillParameter � REPORT_CONNECTION � FILTER � 
JASPER_REPORT � 
REPORT_LOCALE � REPORT_TIME_ZONE � REPORT_TEMPLATES � REPORT_MAX_COUNT � REPORT_SCRIPTLET � JASPER_REPORTS_CONTEXT � REPORT_FILE_RESOLVER � REPORT_FORMAT_FACTORY � REPORT_PARAMETERS_MAP � REPORT_RESOURCE_BUNDLE � REPORT_DATA_SOURCE � REPORT_CONTEXT � REPORT_CLASS_LOADER � REPORT_URL_HANDLER_FACTORY � REPORT_VIRTUALIZER � SORT_FIELDS � valor5 � ,net/sf/jasperreports/engine/fill/JRFillField � valor4 � valor1 � valor3 � valor2 � 	descricao � PAGE_NUMBER � /net/sf/jasperreports/engine/fill/JRFillVariable � MASTER_CURRENT_PAGE � MASTER_TOTAL_PAGES � 
COLUMN_NUMBER � REPORT_COUNT � 
PAGE_COUNT � COLUMN_COUNT evaluate (I)Ljava/lang/Object; 
Exceptions � java/lang/Throwable � java/lang/Integer
 � � ) � (I)V
 � � � � getValue ()Ljava/lang/Object; � java/lang/String evaluateOld
 � � � � getOldValue evaluateEstimated 
SourceFile !     !                 	     
               
                                                                                                ! "    # "    $ "    % "    & "    ' "    ( "     ) *  +  J     �*� ,*� .*� 0*� 2*� 4*� 6*� 8*� :*� <*� >*� @*� B*� D*� F*� H*� J*� L*� N*� P*� R*� T*� V*� X*� Z*� \*� ^*� `*� b*� d*� f*� h*� j*� l*� n�    p   � #      	          "  '   , ! 1 " 6 # ; $ @ % E & J ' O ( T ) Y * ^ + c , h - m . r / w 0 | 1 � 2 � 3 � 4 � 5 � 6 � 7 � 8 � 9 �   q r  +   4     *+� s*,� w*-� z�    p       E  F 
 G  H  u v  +  �    -*+}�  � �� .*+��  � �� 0*+��  � �� 2*+��  � �� 4*+��  � �� 6*+��  � �� 8*+��  � �� :*+��  � �� <*+��  � �� >*+��  � �� @*+��  � �� B*+��  � �� D*+��  � �� F*+��  � �� H*+��  � �� J*+��  � �� L*+��  � �� N*+��  � �� P*+��  � �� R*+��  � �� T�    p   V    P  Q  R - S < T K U Z V i W x X � Y � Z � [ � \ � ] � ^ � _ � ` � a b c, d  y v  +   �     [*+��  � �� V*+��  � �� X*+��  � �� Z*+��  � �� \*+��  � �� ^*+��  � �� `�    p       l  m  n - o < p K q Z r  | v  +   �     j*+��  � �� b*+��  � �� d*+��  � �� f*+ù  � �� h*+Ź  � �� j*+ǹ  � �� l*+ɹ  � �� n�    p   "    z  {  | - } < ~ K  Z � i �  � �  �     � +  �     �M�   �       
   E   Q   ]   i   u   �   �   �   �   �   �   �   �   � �Y� �M� �� �Y� �M� �� �Y� �M� �� �Y� �M� �� �Y� �M� x� �Y� �M� l� �Y� �M� `� �Y� �M� T*� `� �� �M� F*� Z� �� �M� 8*� ^� �� �M� **� \� �� �M� *� X� �� �M� *� V� �� �M,�    p   z    �  � H � Q � T � ] � ` � i � l � u � x � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � � �  � �  �     � +  �     �M�   �       
   E   Q   ]   i   u   �   �   �   �   �   �   �   �   � �Y� �M� �� �Y� �M� �� �Y� �M� �� �Y� �M� �� �Y� �M� x� �Y� �M� l� �Y� �M� `� �Y� �M� T*� `� �� �M� F*� Z� �� �M� 8*� ^� �� �M� **� \� �� �M� *� X� �� �M� *� V� �� �M,�    p   z    �  � H � Q � T � ] � ` � i � l � u � x � � � �  � � � �
 � � � � � � � � � �# �$ �( �0  � �  �     � +  �     �M�   �       
   E   Q   ]   i   u   �   �   �   �   �   �   �   �   � �Y� �M� �� �Y� �M� �� �Y� �M� �� �Y� �M� �� �Y� �M� x� �Y� �M� l� �Y� �M� `� �Y� �M� T*� `� �� �M� F*� Z� �� �M� 8*� ^� �� �M� **� \� �� �M� *� X� �� �M� *� V� �� �M,�    p   z   9 ; H? Q@ TD ]E `I iJ lN uO xS �T �X �Y �] �^ �b �c �g �h �l �m �q �r �v �w �{ �| �� ��  �    t _1527220399524_869119t 2net.sf.jasperreports.engine.design.JRJavacCompiler